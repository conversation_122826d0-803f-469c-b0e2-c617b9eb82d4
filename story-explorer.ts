/**
 * Interactive Story Explorer Component
 * Immersive navigation modes for exploring the Harry Potter story universe
 */

import { storyIntelligence, Character, Relationship } from './story-intelligence';

interface ExplorerState {
  currentMode: 'character-journey' | 'relationship-focus' | 'thematic-exploration' | 'temporal-navigation' | 'network-exploration';
  selectedCharacter: string | null;
  selectedRelationship: string | null;
  selectedTheme: string | null;
  currentChapter: number;
  explorationPath: string[];
  immersiveMode: boolean;
  autoPlay: boolean;
}

interface NavigationNode {
  id: string;
  type: 'character' | 'relationship' | 'theme' | 'chapter' | 'event';
  title: string;
  description: string;
  connections: string[];
  metadata: any;
}

class InteractiveStoryExplorer {
  private container: HTMLElement | null = null;
  private state: ExplorerState = {
    currentMode: 'character-journey',
    selectedCharacter: null,
    selectedRelationship: null,
    selectedTheme: null,
    currentChapter: 1,
    explorationPath: [],
    immersiveMode: false,
    autoPlay: false
  };
  private navigationGraph: Map<string, NavigationNode> = new Map();

  async initialize(containerId: string): Promise<void> {
    console.log('🌟 Initializing Interactive Story Explorer...');
    
    this.container = document.getElementById(containerId);
    if (!this.container) {
      throw new Error(`Explorer container ${containerId} not found`);
    }

    await storyIntelligence.initialize();
    this.buildNavigationGraph();
    this.render();
    this.setupEventListeners();
    
    console.log('✅ Interactive Story Explorer initialized');
  }

  private buildNavigationGraph(): void {
    // Build character nodes
    storyIntelligence.getAllCharacters().forEach(char => {
      this.navigationGraph.set(char.character_id, {
        id: char.character_id,
        type: 'character',
        title: char.character_name,
        description: char.character_evolution?.initial_state || '',
        connections: this.getCharacterConnections(char),
        metadata: char
      });
    });

    // Build relationship nodes
    storyIntelligence.getAllRelationships().forEach(rel => {
      this.navigationGraph.set(rel.relationship_id, {
        id: rel.relationship_id,
        type: 'relationship',
        title: `${storyIntelligence.getCharacter(rel.character_a_id)?.character_name} ↔ ${storyIntelligence.getCharacter(rel.character_b_id)?.character_name}`,
        description: rel.relationship_summary,
        connections: [rel.character_a_id, rel.character_b_id],
        metadata: rel
      });
    });

    // Build theme nodes
    const themes = storyIntelligence.getBookMetadata()?.overall_themes || [];
    themes.forEach((theme, index) => {
      this.navigationGraph.set(`theme_${index}`, {
        id: `theme_${index}`,
        type: 'theme',
        title: theme,
        description: `Explore the theme of ${theme} throughout the story`,
        connections: this.getThemeConnections(theme),
        metadata: { theme, index }
      });
    });
  }

  private getCharacterConnections(character: Character): string[] {
    const relationships = storyIntelligence.getRelationshipsByCharacter(character.character_id);
    return relationships.map(rel => 
      rel.character_a_id === character.character_id ? rel.character_b_id : rel.character_a_id
    );
  }

  private getThemeConnections(theme: string): string[] {
    // Find characters most connected to this theme based on their motivations and conflicts
    return storyIntelligence.getAllCharacters()
      .filter(char => 
        char.core_motivations?.some(motivation => 
          motivation.toLowerCase().includes(theme.toLowerCase().split(' ')[0])
        ) ||
        char.primary_conflicts?.some(conflict => 
          conflict.toLowerCase().includes(theme.toLowerCase().split(' ')[0])
        )
      )
      .map(char => char.character_id)
      .slice(0, 5);
  }

  private render(): void {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="story-explorer ${this.state.immersiveMode ? 'immersive' : ''}">
        ${this.renderHeader()}
        ${this.renderModeSelector()}
        ${this.renderMainExploration()}
        ${this.renderNavigationPanel()}
        ${this.renderContextualRecommendations()}
      </div>
    `;

    this.applyStyles();
    this.setupEventListeners();
  }

  private renderHeader(): string {
    return `
      <div class="explorer-header">
        <h2>🌟 Interactive Story Explorer</h2>
        <p>Immerse yourself in the Harry Potter universe through multiple exploration modes</p>
        <div class="exploration-path">
          <span class="path-label">Journey:</span>
          <div class="path-breadcrumbs">
            ${this.state.explorationPath.map((nodeId, index) => {
              const node = this.navigationGraph.get(nodeId);
              return `
                <span class="breadcrumb ${index === this.state.explorationPath.length - 1 ? 'current' : ''}" 
                      data-node="${nodeId}">
                  ${node?.title || nodeId}
                </span>
              `;
            }).join('<span class="path-separator">→</span>')}
          </div>
        </div>
      </div>
    `;
  }

  private renderModeSelector(): string {
    const modes = [
      { id: 'character-journey', icon: '👤', title: 'Character Journey', desc: 'Follow character development paths' },
      { id: 'relationship-focus', icon: '🔗', title: 'Relationship Focus', desc: 'Explore relationship dynamics' },
      { id: 'thematic-exploration', icon: '🎭', title: 'Thematic Exploration', desc: 'Navigate by story themes' },
      { id: 'temporal-navigation', icon: '⏰', title: 'Temporal Navigation', desc: 'Journey through time' },
      { id: 'network-exploration', icon: '🕸️', title: 'Network Exploration', desc: 'Discover story connections' }
    ];

    return `
      <div class="mode-selector">
        <div class="mode-tabs">
          ${modes.map(mode => `
            <button class="mode-tab ${this.state.currentMode === mode.id ? 'active' : ''}" 
                    data-mode="${mode.id}">
              <div class="mode-icon">${mode.icon}</div>
              <div class="mode-info">
                <div class="mode-title">${mode.title}</div>
                <div class="mode-desc">${mode.desc}</div>
              </div>
            </button>
          `).join('')}
        </div>
        
        <div class="explorer-controls">
          <label class="control-toggle">
            <input type="checkbox" id="immersive-mode" ${this.state.immersiveMode ? 'checked' : ''}>
            <span>🎬 Immersive Mode</span>
          </label>
          <label class="control-toggle">
            <input type="checkbox" id="auto-play" ${this.state.autoPlay ? 'checked' : ''}>
            <span>▶️ Auto-Play</span>
          </label>
        </div>
      </div>
    `;
  }

  private renderMainExploration(): string {
    switch (this.state.currentMode) {
      case 'character-journey':
        return this.renderCharacterJourney();
      case 'relationship-focus':
        return this.renderRelationshipFocus();
      case 'thematic-exploration':
        return this.renderThematicExploration();
      case 'temporal-navigation':
        return this.renderTemporalNavigation();
      case 'network-exploration':
        return this.renderNetworkExploration();
      default:
        return this.renderCharacterJourney();
    }
  }

  private renderCharacterJourney(): string {
    const characters = storyIntelligence.getAllCharacters()
      .filter(char => char.character_evolution)
      .sort((a, b) => b.overall_importance - a.overall_importance);

    if (!this.state.selectedCharacter && characters.length > 0) {
      this.state.selectedCharacter = characters[0].character_id;
    }

    const selectedChar = this.state.selectedCharacter ? 
      storyIntelligence.getCharacter(this.state.selectedCharacter) : null;

    return `
      <div class="exploration-main character-journey-mode">
        <div class="character-selector-panel">
          <h3>🎭 Choose Your Character</h3>
          <div class="character-grid">
            ${characters.slice(0, 8).map(char => `
              <div class="character-journey-card ${this.state.selectedCharacter === char.character_id ? 'selected' : ''}" 
                   data-character="${char.character_id}">
                <div class="char-avatar">${this.getCharacterEmoji(char.character_archetype)}</div>
                <div class="char-info">
                  <div class="char-name">${char.character_name}</div>
                  <div class="char-archetype">${char.character_archetype}</div>
                  <div class="char-importance">★ ${char.overall_importance}/10</div>
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        ${selectedChar ? `
          <div class="journey-visualization">
            <h3>🗺️ ${selectedChar.character_name}'s Journey</h3>
            ${this.renderCharacterJourneyPath(selectedChar)}
          </div>
        ` : ''}
      </div>
    `;
  }

  private renderCharacterJourneyPath(character: Character): string {
    const evolution = character.character_evolution;
    const chapters = character.chapter_by_chapter_summary;
    
    return `
      <div class="journey-path">
        <div class="journey-start">
          <div class="journey-node start-node">
            <div class="node-icon">🌱</div>
            <div class="node-content">
              <h4>Beginning</h4>
              <p>${evolution.initial_state}</p>
            </div>
          </div>
        </div>

        <div class="journey-milestones">
          ${evolution.major_turning_points.map((tp, index) => `
            <div class="journey-milestone" data-chapter="${tp.chapter}">
              <div class="milestone-connector"></div>
              <div class="journey-node milestone-node">
                <div class="node-icon">⚡</div>
                <div class="node-content">
                  <h4>Chapter ${tp.chapter}: ${tp.event}</h4>
                  <p>${tp.impact}</p>
                  <button class="explore-moment" data-chapter="${tp.chapter}">
                    🔍 Explore This Moment
                  </button>
                </div>
              </div>
            </div>
          `).join('')}
        </div>

        <div class="journey-end">
          <div class="journey-node end-node">
            <div class="node-icon">🎯</div>
            <div class="node-content">
              <h4>Current State</h4>
              <p>${evolution.final_state}</p>
              <div class="growth-indicator ${evolution.growth_trajectory}">
                ${evolution.growth_trajectory.toUpperCase()} Growth
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private getCharacterEmoji(archetype: string): string {
    const emojiMap: Record<string, string> = {
      'protagonist': '⚡',
      'mentor': '🧙‍♂️',
      'antagonist': '🐍',
      'ally': '🤝',
      'comic relief': '😄',
      'authority figure': '👑',
      'family': '👨‍👩‍👧‍👦',
      'friend': '👫',
      'rival': '⚔️'
    };

    for (const [key, emoji] of Object.entries(emojiMap)) {
      if (archetype.toLowerCase().includes(key)) return emoji;
    }
    return '👤';
  }

  private renderRelationshipFocus(): string {
    const relationships = storyIntelligence.getAllRelationships()
      .sort((a, b) => b.overall_strength - a.overall_strength);

    return `
      <div class="exploration-main relationship-focus-mode">
        <div class="relationship-selector">
          <h3>🔗 Explore Relationships</h3>
          <div class="relationship-grid">
            ${relationships.slice(0, 6).map(rel => {
              const charA = storyIntelligence.getCharacter(rel.character_a_id);
              const charB = storyIntelligence.getCharacter(rel.character_b_id);
              return `
                <div class="relationship-focus-card ${this.state.selectedRelationship === rel.relationship_id ? 'selected' : ''}"
                     data-relationship="${rel.relationship_id}">
                  <div class="rel-characters">
                    <div class="rel-char">${charA?.character_name}</div>
                    <div class="rel-connector ${rel.relationship_classification}">
                      ${this.getRelationshipIcon(rel.relationship_classification)}
                    </div>
                    <div class="rel-char">${charB?.character_name}</div>
                  </div>
                  <div class="rel-info">
                    <div class="rel-type">${rel.relationship_classification}</div>
                    <div class="rel-strength">Strength: ${rel.overall_strength}/10</div>
                  </div>
                </div>
              `;
            }).join('')}
          </div>
        </div>

        ${this.state.selectedRelationship ? this.renderRelationshipJourney() : ''}
      </div>
    `;
  }

  private renderRelationshipJourney(): string {
    const relationship = storyIntelligence.getRelationship(this.state.selectedRelationship!);
    if (!relationship) return '';

    const charA = storyIntelligence.getCharacter(relationship.character_a_id);
    const charB = storyIntelligence.getCharacter(relationship.character_b_id);

    return `
      <div class="relationship-journey">
        <h3>💫 ${charA?.character_name} & ${charB?.character_name} Relationship Journey</h3>
        <div class="relationship-evolution-timeline">
          <div class="rel-initial-state">
            <div class="rel-timeline-node">
              <div class="node-icon">🌱</div>
              <div class="node-content">
                <h4>Initial Dynamic</h4>
                <p>${relationship.relationship_evolution.initial_dynamic}</p>
              </div>
            </div>
          </div>

          ${relationship.relationship_evolution.key_developments.map(dev => `
            <div class="rel-development" data-chapter="${dev.chapter}">
              <div class="rel-timeline-connector"></div>
              <div class="rel-timeline-node">
                <div class="node-icon">🔄</div>
                <div class="node-content">
                  <h4>Chapter ${dev.chapter}: ${dev.event}</h4>
                  <p>${dev.new_dynamic}</p>
                  <div class="relationship-change ${dev.relationship_change > 0 ? 'positive' : 'negative'}">
                    ${dev.relationship_change > 0 ? '↗️' : '↘️'} ${Math.abs(dev.relationship_change)} change
                  </div>
                </div>
              </div>
            </div>
          `).join('')}

          <div class="rel-current-state">
            <div class="rel-timeline-node">
              <div class="node-icon">🎯</div>
              <div class="node-content">
                <h4>Current Status</h4>
                <p>${relationship.relationship_evolution.current_status}</p>
                <div class="relationship-stability ${relationship.relationship_stability}">
                  ${relationship.relationship_stability.toUpperCase()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderThematicExploration(): string {
    const themes = storyIntelligence.getBookMetadata()?.overall_themes || [];

    return `
      <div class="exploration-main thematic-exploration-mode">
        <div class="theme-selector">
          <h3>🎭 Explore Story Themes</h3>
          <div class="theme-grid">
            ${themes.map((theme, index) => `
              <div class="theme-card ${this.state.selectedTheme === theme ? 'selected' : ''}"
                   data-theme="${theme}">
                <div class="theme-icon">${this.getThemeIcon(theme)}</div>
                <div class="theme-title">${theme}</div>
                <div class="theme-connections">
                  ${this.getThemeConnections(theme).length} connected characters
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        ${this.state.selectedTheme ? this.renderThemeExploration() : ''}
      </div>
    `;
  }

  private renderThemeExploration(): string {
    const connectedCharacters = this.getThemeConnections(this.state.selectedTheme!);

    return `
      <div class="theme-exploration">
        <h3>🌟 Exploring: ${this.state.selectedTheme}</h3>
        <div class="theme-character-network">
          ${connectedCharacters.map(charId => {
            const character = storyIntelligence.getCharacter(charId);
            if (!character) return '';

            return `
              <div class="theme-character-node" data-character="${charId}">
                <div class="char-avatar">${this.getCharacterEmoji(character.character_archetype)}</div>
                <div class="char-name">${character.character_name}</div>
                <div class="theme-connection">
                  ${this.getThemeConnectionReason(character, this.state.selectedTheme!)}
                </div>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    `;
  }

  private renderTemporalNavigation(): string {
    return `
      <div class="exploration-main temporal-navigation-mode">
        <div class="chapter-timeline">
          <h3>⏰ Navigate Through Time</h3>
          <div class="timeline-slider">
            <input type="range" id="chapter-slider" min="1" max="8" value="${this.state.currentChapter}"
                   class="chapter-range">
            <div class="chapter-labels">
              ${Array.from({length: 8}, (_, i) => `
                <span class="chapter-label ${this.state.currentChapter === i + 1 ? 'active' : ''}"
                      data-chapter="${i + 1}">Ch ${i + 1}</span>
              `).join('')}
            </div>
          </div>
        </div>

        <div class="chapter-exploration">
          ${this.renderChapterDetails(this.state.currentChapter)}
        </div>
      </div>
    `;
  }

  private renderChapterDetails(chapterNumber: number): string {
    const charactersInChapter = storyIntelligence.getAllCharacters()
      .filter(char => char.chapter_by_chapter_summary.some(ch => ch.chapter_number === chapterNumber))
      .sort((a, b) => {
        const aData = a.chapter_by_chapter_summary.find(ch => ch.chapter_number === chapterNumber);
        const bData = b.chapter_by_chapter_summary.find(ch => ch.chapter_number === chapterNumber);
        const presenceOrder = { 'major': 3, 'supporting': 2, 'mentioned': 1 };
        return (presenceOrder[bData?.presence_level as keyof typeof presenceOrder] || 0) -
               (presenceOrder[aData?.presence_level as keyof typeof presenceOrder] || 0);
      });

    return `
      <div class="chapter-details">
        <h4>📖 Chapter ${chapterNumber} Overview</h4>
        <div class="chapter-characters">
          <h5>Characters Present (${charactersInChapter.length}):</h5>
          <div class="chapter-char-grid">
            ${charactersInChapter.map(character => {
              const chapterData = character.chapter_by_chapter_summary.find(ch => ch.chapter_number === chapterNumber);
              if (!chapterData) return '';

              return `
                <div class="chapter-character-card" data-character="${character.character_id}">
                  <div class="char-avatar">${this.getCharacterEmoji(character.character_archetype)}</div>
                  <div class="char-info">
                    <div class="char-name">${character.character_name}</div>
                    <div class="presence-level ${chapterData.presence_level}">${chapterData.presence_level}</div>
                    ${chapterData.emotional_state !== 'N/A' ? `
                      <div class="emotional-state">${chapterData.emotional_state}</div>
                    ` : ''}
                    ${chapterData.key_actions.length > 0 ? `
                      <div class="key-actions-preview">
                        <strong>Key Actions:</strong>
                        <ul>
                          ${chapterData.key_actions.slice(0, 2).map(action => `<li>${action}</li>`).join('')}
                          ${chapterData.key_actions.length > 2 ? `<li><em>+${chapterData.key_actions.length - 2} more...</em></li>` : ''}
                        </ul>
                      </div>
                    ` : ''}
                  </div>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      </div>
    `;
  }

  private renderNetworkExploration(): string {
    const networkAnalysis = storyIntelligence.getNetworkAnalysis();

    return `
      <div class="exploration-main network-exploration-mode">
        <div class="network-overview">
          <h3>🕸️ Story Network Analysis</h3>
          <div class="network-stats">
            <div class="network-stat">
              <div class="stat-value">${storyIntelligence.getAllCharacters().length}</div>
              <div class="stat-label">Characters</div>
            </div>
            <div class="network-stat">
              <div class="stat-value">${storyIntelligence.getAllRelationships().length}</div>
              <div class="stat-label">Relationships</div>
            </div>
            <div class="network-stat">
              <div class="stat-value">${networkAnalysis?.character_clusters?.length || 0}</div>
              <div class="stat-label">Character Clusters</div>
            </div>
          </div>
        </div>

        <div class="network-clusters">
          <h4>Character Clusters</h4>
          <div class="cluster-grid">
            ${networkAnalysis?.character_clusters?.map(cluster => `
              <div class="cluster-card" data-cluster="${cluster.cluster_name}">
                <h5>${cluster.cluster_name}</h5>
                <div class="cluster-type">${cluster.cluster_type}</div>
                <div class="binding-factor">${cluster.binding_factor}</div>
                <div class="cluster-members">
                  ${cluster.members.map(memberId => {
                    const character = storyIntelligence.getCharacter(memberId);
                    return character ? `<span class="member">${character.character_name}</span>` : '';
                  }).join(', ')}
                </div>
              </div>
            `).join('') || '<p>No cluster data available</p>'}
          </div>
        </div>
      </div>
    `;
  }

  private renderNavigationPanel(): string {
    return `
      <div class="navigation-panel">
        <h4>🧭 Smart Navigation</h4>
        <div class="quick-actions">
          <button class="nav-action" data-action="random-character">
            🎲 Random Character
          </button>
          <button class="nav-action" data-action="strongest-relationship">
            💪 Strongest Bond
          </button>
          <button class="nav-action" data-action="major-turning-point">
            ⚡ Major Moment
          </button>
          <button class="nav-action" data-action="reset-journey">
            🔄 Reset Journey
          </button>
        </div>
      </div>
    `;
  }

  private renderContextualRecommendations(): string {
    const recommendations = this.getContextualRecommendations();

    return `
      <div class="contextual-recommendations">
        <h4>💡 Explore Next</h4>
        <div class="recommendations-grid">
          ${recommendations.map(rec => `
            <div class="recommendation-card" data-action="${rec.action}" data-target="${rec.target}">
              <div class="rec-icon">${rec.icon}</div>
              <div class="rec-content">
                <div class="rec-title">${rec.title}</div>
                <div class="rec-description">${rec.description}</div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  private getContextualRecommendations(): Array<{icon: string, title: string, description: string, action: string, target: string}> {
    const recommendations = [];

    if (this.state.selectedCharacter) {
      const character = storyIntelligence.getCharacter(this.state.selectedCharacter);
      const relationships = storyIntelligence.getRelationshipsByCharacter(this.state.selectedCharacter);

      if (relationships.length > 0) {
        const strongestRel = relationships.sort((a, b) => b.overall_strength - a.overall_strength)[0];
        const otherChar = strongestRel.character_a_id === this.state.selectedCharacter ?
          strongestRel.character_b_id : strongestRel.character_a_id;
        const otherCharName = storyIntelligence.getCharacter(otherChar)?.character_name;

        recommendations.push({
          icon: '🔗',
          title: `Explore relationship with ${otherCharName}`,
          description: 'Dive into their strongest connection',
          action: 'explore-relationship',
          target: strongestRel.relationship_id
        });
      }
    }

    recommendations.push(
      {
        icon: '🎭',
        title: 'Discover character archetypes',
        description: 'See how different character types interact',
        action: 'explore-archetypes',
        target: 'archetypes'
      },
      {
        icon: '⏰',
        title: 'Timeline exploration',
        description: 'Navigate through story chronologically',
        action: 'switch-mode',
        target: 'temporal-navigation'
      }
    );

    return recommendations.slice(0, 3);
  }

  private getRelationshipIcon(type: string): string {
    const iconMap: Record<string, string> = {
      'friendship': '👫',
      'family': '👨‍👩‍👧‍👦',
      'romantic': '💕',
      'mentorship': '👨‍🏫',
      'rivalry': '⚔️',
      'alliance': '🤝',
      'conflict': '⚡',
      'professional': '💼'
    };

    for (const [key, icon] of Object.entries(iconMap)) {
      if (type.toLowerCase().includes(key)) return icon;
    }
    return '🔗';
  }

  private getThemeIcon(theme: string): string {
    const iconMap: Record<string, string> = {
      'love': '💕',
      'friendship': '👫',
      'sacrifice': '⚔️',
      'power': '👑',
      'death': '💀',
      'family': '👨‍👩‍👧‍👦',
      'loyalty': '🛡️',
      'courage': '🦁',
      'magic': '✨',
      'good vs evil': '⚖️',
      'coming of age': '🌱',
      'identity': '🪞'
    };

    for (const [key, icon] of Object.entries(iconMap)) {
      if (theme.toLowerCase().includes(key)) return icon;
    }
    return '🎭';
  }

  private getThemeConnectionReason(character: Character, theme: string): string {
    const motivations = character.core_motivations || [];
    const conflicts = character.primary_conflicts || [];

    const themeKeyword = theme.toLowerCase().split(' ')[0];

    for (const motivation of motivations) {
      if (motivation.toLowerCase().includes(themeKeyword)) {
        return `Motivated by ${motivation.toLowerCase()}`;
      }
    }

    for (const conflict of conflicts) {
      if (conflict.toLowerCase().includes(themeKeyword)) {
        return `Conflicts with ${conflict.toLowerCase()}`;
      }
    }

    return `Connected to ${theme}`;
  }

  private setupEventListeners(): void {
    // Mode selection - use container to scope the query
    this.container?.querySelectorAll('.mode-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const mode = (e.currentTarget as HTMLElement).dataset.mode as any;
        if (mode && mode !== this.state.currentMode) {
          console.log(`Switching to mode: ${mode}`);
          this.state.currentMode = mode;
          this.state.selectedCharacter = null; // Reset character selection when changing modes
          this.state.selectedRelationship = null; // Reset relationship selection
          this.render();
        }
      });
    });

    // Character selection
    this.container?.querySelectorAll('.character-journey-card').forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const characterId = (e.currentTarget as HTMLElement).dataset.character;
        if (characterId) {
          this.state.selectedCharacter = characterId;
          this.addToExplorationPath(characterId);
          this.render();
        }
      });
    });

    // Relationship selection
    this.container?.querySelectorAll('.relationship-focus-card').forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const relationshipId = (e.currentTarget as HTMLElement).dataset.relationship;
        if (relationshipId) {
          this.state.selectedRelationship = relationshipId;
          this.addToExplorationPath(relationshipId);
          this.render();
        }
      });
    });

    // Theme selection
    this.container?.querySelectorAll('.theme-card').forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const theme = (e.currentTarget as HTMLElement).dataset.theme;
        if (theme) {
          this.state.selectedTheme = theme;
          this.addToExplorationPath(`theme_${theme}`);
          this.render();
        }
      });
    });

    // Chapter navigation
    const chapterSlider = this.container?.querySelector('#chapter-slider') as HTMLInputElement;
    chapterSlider?.addEventListener('input', (e) => {
      this.state.currentChapter = parseInt((e.target as HTMLInputElement).value);
      this.render();
    });

    // Chapter labels
    this.container?.querySelectorAll('.chapter-label').forEach(label => {
      label.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const chapter = parseInt((e.currentTarget as HTMLElement).dataset.chapter || '1');
        this.state.currentChapter = chapter;
        const slider = this.container?.querySelector('#chapter-slider') as HTMLInputElement;
        if (slider) slider.value = chapter.toString();
        this.render();
      });
    });

    // Chapter character cards
    this.container?.querySelectorAll('.chapter-character-card').forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const characterId = (e.currentTarget as HTMLElement).dataset.character;
        if (characterId) {
          this.state.selectedCharacter = characterId;
          this.state.currentMode = 'character-journey';
          this.addToExplorationPath(characterId);
          this.render();
        }
      });
    });

    // Control toggles
    const immersiveToggle = this.container?.querySelector('#immersive-mode') as HTMLInputElement;
    const autoPlayToggle = this.container?.querySelector('#auto-play') as HTMLInputElement;

    immersiveToggle?.addEventListener('change', (e) => {
      this.state.immersiveMode = (e.target as HTMLInputElement).checked;
      this.render();
    });

    autoPlayToggle?.addEventListener('change', (e) => {
      this.state.autoPlay = (e.target as HTMLInputElement).checked;
      if (this.state.autoPlay) {
        this.startAutoPlay();
      }
    });

    // Navigation actions
    this.container?.querySelectorAll('.nav-action').forEach(action => {
      action.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const actionType = (e.currentTarget as HTMLElement).dataset.action;
        this.handleNavigationAction(actionType!);
      });
    });

    // Recommendations
    this.container?.querySelectorAll('.recommendation-card').forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const action = (e.currentTarget as HTMLElement).dataset.action;
        const target = (e.currentTarget as HTMLElement).dataset.target;
        this.handleRecommendationAction(action!, target!);
      });
    });

    // Network node interactions (for network exploration mode)
    this.container?.querySelectorAll('.network-node').forEach(node => {
      node.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const characterId = (e.currentTarget as HTMLElement).dataset.character;
        if (characterId) {
          this.state.selectedCharacter = characterId;
          this.state.currentMode = 'character-journey';
          this.addToExplorationPath(characterId);
          this.render();
        }
      });
    });
  }

  private addToExplorationPath(nodeId: string): void {
    if (this.state.explorationPath[this.state.explorationPath.length - 1] !== nodeId) {
      this.state.explorationPath.push(nodeId);
      if (this.state.explorationPath.length > 10) {
        this.state.explorationPath = this.state.explorationPath.slice(-10);
      }
    }
  }

  private handleNavigationAction(action: string): void {
    switch (action) {
      case 'random-character':
        const characters = storyIntelligence.getAllCharacters();
        const randomChar = characters[Math.floor(Math.random() * characters.length)];
        this.state.selectedCharacter = randomChar.character_id;
        this.state.currentMode = 'character-journey';
        this.addToExplorationPath(randomChar.character_id);
        break;
      case 'strongest-relationship':
        const relationships = storyIntelligence.getStrongestRelationships(1);
        if (relationships.length > 0) {
          this.state.selectedRelationship = relationships[0].relationship_id;
          this.state.currentMode = 'relationship-focus';
          this.addToExplorationPath(relationships[0].relationship_id);
        }
        break;
      case 'reset-journey':
        this.state.explorationPath = [];
        this.state.selectedCharacter = null;
        this.state.selectedRelationship = null;
        this.state.selectedTheme = null;
        break;
    }
    this.render();
  }

  private handleRecommendationAction(action: string, target: string): void {
    switch (action) {
      case 'explore-relationship':
        this.state.selectedRelationship = target;
        this.state.currentMode = 'relationship-focus';
        this.addToExplorationPath(target);
        break;
      case 'switch-mode':
        this.state.currentMode = target as any;
        break;
    }
    this.render();
  }

  private startAutoPlay(): void {
    // Auto-play functionality for guided exploration
    if (!this.state.autoPlay) return;

    setTimeout(() => {
      if (this.state.autoPlay) {
        this.handleNavigationAction('random-character');
        this.startAutoPlay();
      }
    }, 5000);
  }

  private applyStyles(): void {
    if (document.getElementById('story-explorer-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'story-explorer-styles';
    styles.textContent = `
      .story-explorer {
        background: linear-gradient(135deg, #0f0f23, #1a1a2e);
        border-radius: 12px;
        padding: 25px;
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
      }

      .story-explorer::-webkit-scrollbar {
        width: 8px;
      }

      .story-explorer::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      .story-explorer::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        border-radius: 4px;
      }

      .story-explorer::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #44a08d, #4ecdc4);
      }

      .story-explorer.immersive {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 1000;
        border-radius: 0;
        padding: 40px;
        background: linear-gradient(135deg, #0a0a1a, #1a1a2e);
      }

      .explorer-header {
        text-align: center;
        margin-bottom: 30px;
      }

      .explorer-header h2 {
        margin: 0 0 10px 0;
        color: #4ecdc4;
        font-size: 2.5em;
        text-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
      }

      .explorer-header p {
        margin: 0 0 20px 0;
        color: #aaa;
        font-size: 1.2em;
      }

      .exploration-path {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .path-label {
        color: #4ecdc4;
        font-weight: bold;
        margin-right: 10px;
      }

      .path-breadcrumbs {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
      }

      .breadcrumb {
        padding: 4px 12px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        font-size: 0.9em;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .breadcrumb:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .breadcrumb.current {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        color: white;
      }

      .path-separator {
        color: #666;
      }

      .mode-selector {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .mode-tabs {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .mode-tab {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 15px;
        color: white;
      }

      .mode-tab:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
      }

      .mode-tab.active {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        border-color: #4ecdc4;
        box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
      }

      .mode-icon {
        font-size: 2em;
      }

      .mode-info {
        flex: 1;
      }

      .mode-title {
        font-weight: bold;
        font-size: 1.1em;
        margin-bottom: 5px;
      }

      .mode-desc {
        color: #aaa;
        font-size: 0.9em;
      }

      .explorer-controls {
        display: flex;
        gap: 20px;
        justify-content: center;
      }

      .control-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #aaa;
        cursor: pointer;
        font-weight: bold;
      }

      .control-toggle input[type="checkbox"] {
        accent-color: #4ecdc4;
        transform: scale(1.2);
      }

      .exploration-main {
        background: rgba(255, 255, 255, 0.03);
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      /* Character Journey Styles */
      .character-selector-panel {
        margin-bottom: 30px;
      }

      .character-selector-panel h3 {
        color: #4ecdc4;
        margin-bottom: 20px;
        font-size: 1.5em;
      }

      .character-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }

      .character-journey-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .character-journey-card:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
      }

      .character-journey-card.selected {
        background: linear-gradient(135deg, rgba(78, 205, 196, 0.2), rgba(68, 160, 141, 0.2));
        border-color: #4ecdc4;
        box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
      }

      .char-avatar {
        font-size: 2.5em;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .char-info {
        flex: 1;
      }

      .char-name {
        font-weight: bold;
        font-size: 1.1em;
        margin-bottom: 5px;
        color: #fff;
      }

      .char-archetype {
        color: #ffd700;
        font-size: 0.9em;
        text-transform: capitalize;
        margin-bottom: 3px;
      }

      .char-importance {
        color: #4ecdc4;
        font-size: 0.8em;
      }

      /* Journey Path Styles */
      .journey-visualization {
        margin-top: 30px;
      }

      .journey-visualization h3 {
        color: #4ecdc4;
        margin-bottom: 25px;
        font-size: 1.5em;
      }

      .journey-path {
        position: relative;
        padding: 20px 0;
      }

      .journey-start, .journey-end {
        margin: 30px 0;
      }

      .journey-milestones {
        margin: 40px 0;
      }

      .journey-milestone {
        margin: 30px 0;
        position: relative;
      }

      .milestone-connector {
        position: absolute;
        left: 50%;
        top: -15px;
        width: 3px;
        height: 30px;
        background: linear-gradient(to bottom, #4ecdc4, #ffd700);
        transform: translateX(-50%);
      }

      .journey-node {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        max-width: 600px;
        margin: 0 auto;
      }

      .start-node {
        border-left: 4px solid #4caf50;
      }

      .milestone-node {
        border-left: 4px solid #ffd700;
        background: rgba(255, 215, 0, 0.05);
      }

      .end-node {
        border-left: 4px solid #4ecdc4;
      }

      .node-icon {
        position: absolute;
        left: -15px;
        top: 20px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #1a1a2e;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2em;
        border: 2px solid #4ecdc4;
      }

      .milestone-node .node-icon {
        border-color: #ffd700;
      }

      .node-content h4 {
        margin: 0 0 15px 0;
        color: #fff;
        font-size: 1.2em;
      }

      .node-content p {
        margin: 0 0 15px 0;
        color: #ddd;
        line-height: 1.5;
      }

      .explore-moment {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        color: white;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9em;
      }

      .explore-moment:hover {
        background: linear-gradient(135deg, #44a08d, #4ecdc4);
        transform: translateY(-1px);
      }

      .growth-indicator {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 0.9em;
        font-weight: bold;
        text-transform: uppercase;
        margin-top: 10px;
      }

      .growth-indicator.positive {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
      }

      .growth-indicator.negative {
        background: rgba(244, 67, 54, 0.2);
        color: #f44336;
      }

      .growth-indicator.complex {
        background: rgba(255, 152, 0, 0.2);
        color: #ff9800;
      }

      .growth-indicator.overall {
        background: rgba(78, 205, 196, 0.2);
        color: #4ecdc4;
      }

      /* Chapter Details Styles */
      .chapter-details {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
        margin-top: 20px;
      }

      .chapter-details h4 {
        color: #4ecdc4;
        margin-bottom: 20px;
        font-size: 1.4em;
      }

      .chapter-details h5 {
        color: #ffd700;
        margin-bottom: 15px;
        font-size: 1.1em;
      }

      .chapter-char-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 15px;
      }

      .chapter-character-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        gap: 12px;
      }

      .chapter-character-card:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
      }

      .chapter-character-card .char-avatar {
        font-size: 1.8em;
        width: 45px;
        height: 45px;
        flex-shrink: 0;
      }

      .chapter-character-card .char-info {
        flex: 1;
        min-width: 0;
      }

      .chapter-character-card .char-name {
        font-weight: bold;
        margin-bottom: 5px;
        color: #fff;
      }

      .presence-level {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
        text-transform: capitalize;
        margin-bottom: 5px;
      }

      .presence-level.major {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
      }

      .presence-level.supporting {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
      }

      .presence-level.mentioned {
        background: rgba(158, 158, 158, 0.2);
        color: #9e9e9e;
      }

      .emotional-state {
        color: #4ecdc4;
        font-size: 0.9em;
        margin-bottom: 8px;
        font-style: italic;
      }

      .key-actions-preview {
        font-size: 0.85em;
        color: #ddd;
      }

      .key-actions-preview strong {
        color: #ffd700;
        display: block;
        margin-bottom: 5px;
      }

      .key-actions-preview ul {
        margin: 0;
        padding-left: 15px;
        list-style-type: disc;
      }

      .key-actions-preview li {
        margin-bottom: 2px;
        color: #ccc;
      }

      .key-actions-preview em {
        color: #999;
      }

      /* Timeline Slider Styles */
      .timeline-slider {
        margin: 20px 0;
      }

      .chapter-range {
        width: 100%;
        height: 8px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.2);
        outline: none;
        -webkit-appearance: none;
        margin-bottom: 15px;
      }

      .chapter-range::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        cursor: pointer;
        box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
      }

      .chapter-range::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        cursor: pointer;
        border: none;
        box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
      }

      .chapter-labels {
        display: flex;
        justify-content: space-between;
        padding: 0 10px;
      }

      .chapter-label {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.9em;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.1);
      }

      .chapter-label:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .chapter-label.active {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        color: white;
        font-weight: bold;
      }
    `;
    document.head.appendChild(styles);
  }
}

// Create and export singleton instance
export const storyExplorer = new InteractiveStoryExplorer();
