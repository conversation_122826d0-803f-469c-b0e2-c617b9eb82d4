<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<defs>
  <radialGradient id="bgGrad" cx="50%" cy="30%" r="70%">
    <stop offset="0%" style="stop-color:#4b0082;stop-opacity:1" />
    <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
  </radialGradient>
  <radialGradient id="faceGrad" cx="50%" cy="40%" r="60%">
    <stop offset="0%" style="stop-color:#fdbcb4;stop-opacity:1" />
    <stop offset="100%" style="stop-color:#e8a798;stop-opacity:1" />
  </radialGradient>
</defs>
<rect width="100" height="100" fill="url(#bgGrad)"/>
<circle cx="50" cy="35" r="15" fill="url(#faceGrad)" stroke="#d4af37" stroke-width="1"/>
<path d="M25 85C25 70 35.8 55 50 55S75 70 75 85H25Z" fill="url(#faceGrad)" stroke="#d4af37" stroke-width="1"/>
<!-- Eyes -->
<circle cx="45" cy="32" r="2" fill="#2d1810"/>
<circle cx="55" cy="32" r="2" fill="#2d1810"/>
<!-- Wizard hat suggestion -->
<path d="M40 20 Q50 10 60 20 Q55 15 50 17 Q45 15 40 20" fill="#4b0082" stroke="#d4af37" stroke-width="1"/>
<text x="50" y="95" text-anchor="middle" fill="#d4af37" font-family="Arial" font-size="6" font-weight="bold">WIZARD</text>
</svg>
