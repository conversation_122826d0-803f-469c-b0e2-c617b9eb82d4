// Test script to verify chapter data differences
const fs = require('fs');
const pako = require('pako');

console.log('🔍 Testing Chapter Data Differences\n');

// Load and compare chapter data
const chapters = {};
for (let i = 1; i <= 12; i++) {
  try {
    const data = fs.readFileSync(`data/HarryPotter_characters_by_stories_c${i}.json.gz`);
    const graph = JSON.parse(pako.inflate(data, {to: 'string'}));
    chapters[i] = {
      nodes: graph.nodes.length,
      edges: graph.edges.length,
      characters: graph.nodes.map(n => n.attributes.name).sort()
    };
    console.log(`📖 Chapter ${i}: ${chapters[i].nodes} characters, ${chapters[i].edges} relationships`);
    console.log(`   Characters: ${chapters[i].characters.slice(0, 5).join(', ')}${chapters[i].characters.length > 5 ? '...' : ''}`);
  } catch (e) {
    console.log(`❌ Chapter ${i}: ERROR - ${e.message}`);
  }
}

// Load global data for comparison
try {
  const globalData = fs.readFileSync('data/HarryPotter_characters_by_stories_full.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  console.log(`\n🌍 Global View: ${globalGraph.nodes.length} characters, ${globalGraph.edges.length} relationships`);
  
  // Show unique characters per chapter
  console.log('\n🔍 Character Analysis:');
  const allChapterChars = new Set();
  Object.keys(chapters).forEach(ch => {
    chapters[ch].characters.forEach(char => allChapterChars.add(char));
  });
  
  console.log(`Total unique characters across all chapters: ${allChapterChars.size}`);
  console.log(`Global graph characters: ${globalGraph.nodes.length}`);
  
  // Find characters that appear in multiple chapters
  const charCounts = {};
  Object.keys(chapters).forEach(ch => {
    chapters[ch].characters.forEach(char => {
      charCounts[char] = (charCounts[char] || 0) + 1;
    });
  });
  
  const multiChapterChars = Object.keys(charCounts).filter(char => charCounts[char] > 1);
  console.log(`Characters appearing in multiple chapters: ${multiChapterChars.length}`);
  console.log(`Examples: ${multiChapterChars.slice(0, 5).join(', ')}`);
  
} catch (e) {
  console.log(`❌ Global data: ERROR - ${e.message}`);
}

console.log('\n✅ Chapter data verification complete!');
