/**
 * Character Evolution Visualization Component
 * Interactive character development tracking with timelines, emotional states, and turning points
 */

import { storyIntelligence, Character, CharacterEvolution } from './story-intelligence';

interface EvolutionState {
  selectedCharacter: string | null;
  comparisonCharacter: string | null;
  viewMode: 'timeline' | 'emotional' | 'comparison' | 'patterns';
  timelineRange: [number, number];
  showTurningPoints: boolean;
  showEmotionalStates: boolean;
}

class CharacterEvolutionVisualization {
  private container: HTMLElement | null = null;
  private state: EvolutionState = {
    selectedCharacter: null,
    comparisonCharacter: null,
    viewMode: 'timeline',
    timelineRange: [1, 8],
    showTurningPoints: true,
    showEmotionalStates: true
  };

  async initialize(containerId: string): Promise<void> {
    console.log('🧬 Initializing Character Evolution Visualization...');
    
    this.container = document.getElementById(containerId);
    if (!this.container) {
      throw new Error(`Evolution container ${containerId} not found`);
    }

    await storyIntelligence.initialize();
    this.render();
    this.setupEventListeners();
    
    console.log('✅ Character Evolution Visualization initialized');
  }

  private render(): void {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="evolution-visualization">
        ${this.renderHeader()}
        ${this.renderControls()}
        ${this.renderMainContent()}
      </div>
    `;

    this.applyStyles();
    this.setupEventListeners();
  }

  private renderHeader(): string {
    return `
      <div class="evolution-header">
        <h2>🧬 Character Evolution Analysis</h2>
        <p>Explore character development, turning points, and growth trajectories</p>
      </div>
    `;
  }

  private renderControls(): string {
    const characters = storyIntelligence.getAllCharacters()
      .filter(char => char.character_evolution)
      .sort((a, b) => b.overall_importance - a.overall_importance);

    return `
      <div class="evolution-controls">
        <div class="character-selectors">
          <div class="selector-group">
            <label>Primary Character:</label>
            <select id="primary-character-select" class="character-select">
              <option value="">Select a character...</option>
              ${characters.map(char => `
                <option value="${char.character_id}" ${this.state.selectedCharacter === char.character_id ? 'selected' : ''}>
                  ${char.character_name} (${char.character_archetype})
                </option>
              `).join('')}
            </select>
          </div>
          
          <div class="selector-group">
            <label>Compare With:</label>
            <select id="comparison-character-select" class="character-select">
              <option value="">None</option>
              ${characters.map(char => `
                <option value="${char.character_id}" ${this.state.comparisonCharacter === char.character_id ? 'selected' : ''}>
                  ${char.character_name}
                </option>
              `).join('')}
            </select>
          </div>
        </div>

        <div class="view-mode-tabs">
          <button class="view-tab ${this.state.viewMode === 'timeline' ? 'active' : ''}" data-mode="timeline">
            📈 Timeline
          </button>
          <button class="view-tab ${this.state.viewMode === 'emotional' ? 'active' : ''}" data-mode="emotional">
            💭 Emotional Journey
          </button>
          <button class="view-tab ${this.state.viewMode === 'comparison' ? 'active' : ''}" data-mode="comparison">
            ⚖️ Character Comparison
          </button>
          <button class="view-tab ${this.state.viewMode === 'patterns' ? 'active' : ''}" data-mode="patterns">
            🔍 Growth Patterns
          </button>
        </div>

        <div class="visualization-options">
          <label class="option-toggle">
            <input type="checkbox" id="show-turning-points" ${this.state.showTurningPoints ? 'checked' : ''}>
            <span>Show Turning Points</span>
          </label>
          <label class="option-toggle">
            <input type="checkbox" id="show-emotional-states" ${this.state.showEmotionalStates ? 'checked' : ''}>
            <span>Show Emotional States</span>
          </label>
        </div>
      </div>
    `;
  }

  private renderMainContent(): string {
    switch (this.state.viewMode) {
      case 'timeline':
        return this.renderTimelineView();
      case 'emotional':
        return this.renderEmotionalView();
      case 'comparison':
        return this.renderComparisonView();
      case 'patterns':
        return this.renderPatternsView();
      default:
        return this.renderTimelineView();
    }
  }

  private renderTimelineView(): string {
    if (!this.state.selectedCharacter) {
      return `
        <div class="evolution-content">
          <div class="placeholder">
            <div class="placeholder-icon">🧬</div>
            <h3>Select a Character to Begin</h3>
            <p>Choose a character from the dropdown to explore their evolution timeline</p>
          </div>
        </div>
      `;
    }

    const character = storyIntelligence.getCharacter(this.state.selectedCharacter);
    if (!character) return '<div class="error">Character not found</div>';

    return `
      <div class="evolution-content">
        <div class="character-overview">
          ${this.renderCharacterCard(character)}
        </div>
        
        <div class="timeline-container">
          ${this.renderEvolutionTimeline(character)}
        </div>
        
        <div class="turning-points-section">
          ${this.renderTurningPoints(character)}
        </div>
      </div>
    `;
  }

  private renderCharacterCard(character: Character): string {
    const evolution = character.character_evolution;
    const trajectoryColor = this.getTrajectoryColor(evolution.growth_trajectory);
    
    return `
      <div class="character-card-detailed">
        <div class="character-header">
          <h3>${character.character_name}</h3>
          <div class="character-meta">
            <span class="archetype">${character.character_archetype}</span>
            <span class="importance">Importance: ${character.overall_importance}/10</span>
            <span class="trajectory ${evolution.growth_trajectory}" style="color: ${trajectoryColor}">
              ${evolution.growth_trajectory.toUpperCase()} Growth
            </span>
          </div>
        </div>
        
        <div class="evolution-summary">
          <div class="state-comparison">
            <div class="initial-state">
              <h4>Initial State</h4>
              <p>${evolution.initial_state}</p>
            </div>
            <div class="evolution-arrow">→</div>
            <div class="final-state">
              <h4>Final State</h4>
              <p>${evolution.final_state}</p>
            </div>
          </div>
        </div>
        
        <div class="character-stats">
          <div class="stat-item">
            <span>Chapters Present:</span>
            <div class="stat-bar">
              <div class="stat-fill" style="width: ${(character.total_chapters_present / 8) * 100}%"></div>
            </div>
            <span>${character.total_chapters_present}/8</span>
          </div>
          <div class="stat-item">
            <span>Turning Points:</span>
            <div class="stat-bar">
              <div class="stat-fill" style="width: ${(evolution.major_turning_points.length / 5) * 100}%"></div>
            </div>
            <span>${evolution.major_turning_points.length}</span>
          </div>
        </div>
      </div>
    `;
  }

  private renderEvolutionTimeline(character: Character): string {
    const chapters = character.chapter_by_chapter_summary;
    const turningPoints = character.character_evolution.major_turning_points;
    
    return `
      <div class="evolution-timeline">
        <h4>📈 Character Development Timeline</h4>
        <div class="timeline-track">
          ${chapters.map(chapter => {
            const turningPoint = turningPoints.find(tp => tp.chapter === chapter.chapter_number);
            const hasEvent = turningPoint || chapter.key_actions.length > 0;
            
            return `
              <div class="timeline-point ${hasEvent ? 'has-event' : ''} ${turningPoint ? 'turning-point' : ''}" 
                   data-chapter="${chapter.chapter_number}">
                <div class="point-marker">
                  <span class="chapter-number">${chapter.chapter_number}</span>
                  ${turningPoint ? '<div class="turning-point-indicator">⚡</div>' : ''}
                </div>
                
                <div class="point-details">
                  <div class="chapter-info">
                    <strong>Chapter ${chapter.chapter_number}</strong>
                    <span class="presence-level ${chapter.presence_level}">${chapter.presence_level}</span>
                  </div>
                  
                  ${chapter.emotional_state !== 'N/A' ? `
                    <div class="emotional-state">
                      <span class="emotion-label">Emotional State:</span>
                      <span class="emotion-value">${chapter.emotional_state}</span>
                    </div>
                  ` : ''}
                  
                  ${chapter.key_actions.length > 0 ? `
                    <div class="key-actions">
                      <span class="actions-label">Key Actions:</span>
                      <ul>
                        ${chapter.key_actions.map(action => `<li>${action}</li>`).join('')}
                      </ul>
                    </div>
                  ` : ''}
                  
                  ${turningPoint ? `
                    <div class="turning-point-details">
                      <div class="event-description">
                        <strong>⚡ Turning Point:</strong> ${turningPoint.event}
                      </div>
                      <div class="impact-description">
                        <strong>Impact:</strong> ${turningPoint.impact}
                      </div>
                    </div>
                  ` : ''}
                  
                  ${chapter.development_notes ? `
                    <div class="development-notes">
                      <em>${chapter.development_notes}</em>
                    </div>
                  ` : ''}
                </div>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    `;
  }

  private renderTurningPoints(character: Character): string {
    const turningPoints = character.character_evolution.major_turning_points;
    
    return `
      <div class="turning-points-analysis">
        <h4>⚡ Major Turning Points Analysis</h4>
        <div class="turning-points-grid">
          ${turningPoints.map((tp, index) => `
            <div class="turning-point-card">
              <div class="tp-header">
                <span class="tp-number">${index + 1}</span>
                <span class="tp-chapter">Chapter ${tp.chapter}</span>
              </div>
              <div class="tp-content">
                <h5>${tp.event}</h5>
                <p class="tp-impact">${tp.impact}</p>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  private getTrajectoryColor(trajectory: string): string {
    switch (trajectory) {
      case 'positive': return '#4caf50';
      case 'negative': return '#f44336';
      case 'complex': return '#ff9800';
      case 'cyclical': return '#9c27b0';
      case 'overall': return '#4ecdc4';
      default: return '#ffd700';
    }
  }

  private renderEmotionalView(): string {
    if (!this.state.selectedCharacter) {
      return `<div class="placeholder">Select a character to view emotional journey</div>`;
    }

    const character = storyIntelligence.getCharacter(this.state.selectedCharacter);
    if (!character) return '<div class="error">Character not found</div>';

    return `
      <div class="emotional-journey">
        <h4>💭 Emotional Journey Analysis</h4>
        ${this.renderEmotionalTimeline(character)}
        ${this.renderMotivationsAnalysis(character)}
      </div>
    `;
  }

  private renderEmotionalTimeline(character: Character): string {
    const chapters = character.chapter_by_chapter_summary.filter(ch => ch.emotional_state !== 'N/A');

    return `
      <div class="emotional-timeline">
        <div class="emotion-track">
          ${chapters.map(chapter => `
            <div class="emotion-point" data-chapter="${chapter.chapter_number}">
              <div class="emotion-marker">
                <span class="chapter-num">${chapter.chapter_number}</span>
                <div class="emotion-state ${this.getEmotionClass(chapter.emotional_state)}">
                  ${chapter.emotional_state}
                </div>
              </div>
              <div class="emotion-details">
                <div class="goals">
                  <strong>Goals:</strong>
                  <ul>
                    ${chapter.character_goals.map(goal => `<li>${goal}</li>`).join('')}
                  </ul>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  private renderMotivationsAnalysis(character: Character): string {
    return `
      <div class="motivations-analysis">
        <h5>Core Motivations</h5>
        <div class="motivations-grid">
          ${character.core_motivations?.map(motivation => `
            <div class="motivation-card">
              <div class="motivation-icon">🎯</div>
              <span>${motivation}</span>
            </div>
          `).join('') || '<p>No core motivations data available</p>'}
        </div>
      </div>
    `;
  }

  private renderComparisonView(): string {
    if (!this.state.selectedCharacter || !this.state.comparisonCharacter) {
      return `
        <div class="comparison-placeholder">
          <h4>⚖️ Character Comparison</h4>
          <p>Select two characters to compare their evolution patterns</p>
        </div>
      `;
    }

    const char1 = storyIntelligence.getCharacter(this.state.selectedCharacter);
    const char2 = storyIntelligence.getCharacter(this.state.comparisonCharacter);

    if (!char1 || !char2) return '<div class="error">Characters not found</div>';

    return `
      <div class="character-comparison">
        <h4>⚖️ Character Evolution Comparison</h4>
        <div class="comparison-grid">
          <div class="comparison-character">
            <h5>${char1.character_name}</h5>
            ${this.renderComparisonMetrics(char1)}
          </div>
          <div class="comparison-vs">VS</div>
          <div class="comparison-character">
            <h5>${char2.character_name}</h5>
            ${this.renderComparisonMetrics(char2)}
          </div>
        </div>
        ${this.renderEvolutionComparison(char1, char2)}
      </div>
    `;
  }

  private renderComparisonMetrics(character: Character): string {
    const evolution = character.character_evolution;
    return `
      <div class="comparison-metrics">
        <div class="metric">
          <span class="metric-label">Growth Trajectory:</span>
          <span class="metric-value ${evolution.growth_trajectory}">${evolution.growth_trajectory}</span>
        </div>
        <div class="metric">
          <span class="metric-label">Turning Points:</span>
          <span class="metric-value">${evolution.major_turning_points.length}</span>
        </div>
        <div class="metric">
          <span class="metric-label">Importance:</span>
          <span class="metric-value">${character.overall_importance}/10</span>
        </div>
        <div class="metric">
          <span class="metric-label">Archetype:</span>
          <span class="metric-value">${character.character_archetype}</span>
        </div>
      </div>
    `;
  }

  private renderEvolutionComparison(char1: Character, char2: Character): string {
    return `
      <div class="evolution-comparison">
        <h5>Evolution Patterns</h5>
        <div class="pattern-comparison">
          <div class="pattern-item">
            <h6>${char1.character_name}</h6>
            <p><strong>Initial:</strong> ${char1.character_evolution.initial_state}</p>
            <p><strong>Final:</strong> ${char1.character_evolution.final_state}</p>
          </div>
          <div class="pattern-item">
            <h6>${char2.character_name}</h6>
            <p><strong>Initial:</strong> ${char2.character_evolution.initial_state}</p>
            <p><strong>Final:</strong> ${char2.character_evolution.final_state}</p>
          </div>
        </div>
      </div>
    `;
  }

  private renderPatternsView(): string {
    const characters = storyIntelligence.getAllCharacters().filter(char => char.character_evolution);
    const trajectoryGroups = this.groupByTrajectory(characters);

    return `
      <div class="growth-patterns">
        <h4>🔍 Character Growth Patterns</h4>
        <div class="patterns-overview">
          ${Object.entries(trajectoryGroups).map(([trajectory, chars]) => `
            <div class="pattern-group">
              <h5 class="trajectory-header ${trajectory}" style="color: ${this.getTrajectoryColor(trajectory)}">
                ${trajectory.toUpperCase()} Growth (${chars.length} characters)
              </h5>
              <div class="pattern-characters">
                ${chars.map(char => `
                  <div class="pattern-character-card" data-character="${char.character_id}">
                    <span class="char-name">${char.character_name}</span>
                    <span class="char-archetype">${char.character_archetype}</span>
                    <span class="turning-points">${char.character_evolution.major_turning_points.length} TPs</span>
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  private groupByTrajectory(characters: Character[]): Record<string, Character[]> {
    return characters.reduce((groups, char) => {
      const trajectory = char.character_evolution.growth_trajectory;
      if (!groups[trajectory]) groups[trajectory] = [];
      groups[trajectory].push(char);
      return groups;
    }, {} as Record<string, Character[]>);
  }

  private getEmotionClass(emotion: string): string {
    const emotionMap: Record<string, string> = {
      'happy': 'positive',
      'sad': 'negative',
      'angry': 'negative',
      'fearful': 'negative',
      'determined': 'positive',
      'confused': 'neutral',
      'tense': 'negative',
      'relieved': 'positive',
      'shocked': 'negative',
      'dazed': 'neutral',
      'humorous': 'positive'
    };

    const lowerEmotion = emotion.toLowerCase();
    for (const [key, value] of Object.entries(emotionMap)) {
      if (lowerEmotion.includes(key)) return value;
    }
    return 'neutral';
  }

  private setupEventListeners(): void {
    // Character selection
    const primarySelect = this.container?.querySelector('#primary-character-select') as HTMLSelectElement;
    const comparisonSelect = this.container?.querySelector('#comparison-character-select') as HTMLSelectElement;

    primarySelect?.addEventListener('change', (e) => {
      this.state.selectedCharacter = (e.target as HTMLSelectElement).value || null;
      this.render();
    });

    comparisonSelect?.addEventListener('change', (e) => {
      this.state.comparisonCharacter = (e.target as HTMLSelectElement).value || null;
      this.render();
    });

    // View mode tabs
    this.container?.querySelectorAll('.view-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const mode = (e.target as HTMLElement).dataset.mode as any;
        if (mode && mode !== this.state.viewMode) {
          this.state.viewMode = mode;
          this.render();
        }
      });
    });

    // Toggle options
    const turningPointsToggle = this.container?.querySelector('#show-turning-points') as HTMLInputElement;
    const emotionalStatesToggle = this.container?.querySelector('#show-emotional-states') as HTMLInputElement;

    turningPointsToggle?.addEventListener('change', (e) => {
      this.state.showTurningPoints = (e.target as HTMLInputElement).checked;
      this.render();
    });

    emotionalStatesToggle?.addEventListener('change', (e) => {
      this.state.showEmotionalStates = (e.target as HTMLInputElement).checked;
      this.render();
    });

    // Pattern character cards
    this.container?.querySelectorAll('.pattern-character-card').forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const characterId = (e.currentTarget as HTMLElement).dataset.character;
        if (characterId) {
          this.state.selectedCharacter = characterId;
          this.state.viewMode = 'timeline';
          this.render();
        }
      });
    });

    // Timeline point interactions
    this.container?.querySelectorAll('.timeline-point').forEach(point => {
      point.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const chapter = (e.currentTarget as HTMLElement).dataset.chapter;
        if (chapter) {
          console.log(`Exploring Chapter ${chapter} details...`);
          // Could expand to show detailed chapter view
        }
      });
    });

    // Explore moment buttons
    this.container?.querySelectorAll('.explore-moment').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const chapter = (e.currentTarget as HTMLElement).dataset.chapter;
        if (chapter) {
          console.log(`Deep diving into Chapter ${chapter}...`);
          // Could integrate with story explorer
        }
      });
    });
  }

  private applyStyles(): void {
    if (document.getElementById('character-evolution-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'character-evolution-styles';
    styles.textContent = `
      .evolution-visualization {
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        border-radius: 12px;
        padding: 25px;
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
      }

      .evolution-visualization::-webkit-scrollbar {
        width: 8px;
      }

      .evolution-visualization::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      .evolution-visualization::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        border-radius: 4px;
      }

      .evolution-visualization::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #44a08d, #4ecdc4);
      }

      .evolution-header {
        text-align: center;
        margin-bottom: 30px;
      }

      .evolution-header h2 {
        margin: 0 0 10px 0;
        color: #4ecdc4;
        font-size: 2.2em;
      }

      .evolution-header p {
        margin: 0;
        color: #aaa;
        font-size: 1.1em;
      }

      .evolution-controls {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .character-selectors {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }

      .selector-group {
        flex: 1;
      }

      .selector-group label {
        display: block;
        margin-bottom: 8px;
        color: #4ecdc4;
        font-weight: bold;
      }

      .character-select {
        width: 100%;
        padding: 10px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
      }

      .character-select option {
        background: #1a1a2e;
        color: white;
      }

      .view-mode-tabs {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
      }

      .view-tab {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: bold;
      }

      .view-tab:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .view-tab.active {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        color: white;
      }

      .visualization-options {
        display: flex;
        gap: 20px;
      }

      .option-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #aaa;
        cursor: pointer;
      }

      .option-toggle input[type="checkbox"] {
        accent-color: #4ecdc4;
      }

      .evolution-content {
        display: grid;
        gap: 25px;
        color: #ffffff;
      }

      .evolution-content p {
        color: #e0e0e0;
        line-height: 1.6;
      }

      .evolution-content strong {
        color: #ffffff;
      }

      .evolution-content h3, .evolution-content h4 {
        color: #4ecdc4;
      }

      .character-card-detailed {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffffff;
      }

      .character-card-detailed p {
        color: #e0e0e0;
        line-height: 1.6;
      }

      .character-card-detailed strong {
        color: #ffffff;
      }

      .character-header h3 {
        margin: 0 0 15px 0;
        color: #4ecdc4;
        font-size: 1.8em;
      }

      .character-meta {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
      }

      .character-meta span {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.9em;
        font-weight: bold;
      }

      .archetype {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
      }

      .importance {
        background: rgba(78, 205, 196, 0.2);
        color: #4ecdc4;
      }

      .trajectory {
        text-transform: uppercase;
        font-weight: bold;
      }

      .trajectory.positive {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
      }

      .trajectory.negative {
        background: rgba(244, 67, 54, 0.2);
        color: #f44336;
      }

      .trajectory.complex {
        background: rgba(255, 152, 0, 0.2);
        color: #ff9800;
      }

      .trajectory.cyclical {
        background: rgba(156, 39, 176, 0.2);
        color: #9c27b0;
      }

      .trajectory.overall {
        background: rgba(78, 205, 196, 0.2);
        color: #4ecdc4;
      }

      .state-comparison {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 20px;
        align-items: center;
        margin: 20px 0;
      }

      .initial-state, .final-state {
        padding: 15px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.05);
      }

      .initial-state h4, .final-state h4 {
        margin: 0 0 10px 0;
        color: #ffd700;
      }

      .evolution-arrow {
        font-size: 2em;
        color: #4ecdc4;
        text-align: center;
      }

      .character-stats {
        margin-top: 20px;
      }

      .stat-item {
        display: flex;
        align-items: center;
        gap: 15px;
        margin: 10px 0;
      }

      .stat-item span:first-child {
        min-width: 140px;
        color: #aaa;
      }

      .stat-bar {
        flex: 1;
        height: 8px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        overflow: hidden;
      }

      .stat-fill {
        height: 100%;
        background: linear-gradient(90deg, #4ecdc4, #ffd700);
        transition: width 0.3s ease;
      }

      .stat-item span:last-child {
        color: #4ecdc4;
        font-weight: bold;
        min-width: 40px;
      }

      /* Timeline Styles */
      .evolution-timeline {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
        margin: 20px 0;
      }

      .evolution-timeline h4 {
        margin: 0 0 20px 0;
        color: #4ecdc4;
      }

      .timeline-track {
        position: relative;
        padding: 20px 0;
      }

      .timeline-track::before {
        content: '';
        position: absolute;
        left: 30px;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(to bottom, #4ecdc4, #ffd700);
        border-radius: 2px;
      }

      .timeline-point {
        position: relative;
        margin: 30px 0;
        padding-left: 80px;
      }

      .point-marker {
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .chapter-number {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        font-size: 1.1em;
      }

      .timeline-point.turning-point .chapter-number {
        background: linear-gradient(135deg, #ffd700, #ff9800);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
      }

      .turning-point-indicator {
        font-size: 1.5em;
        animation: pulse 2s infinite;
      }

      .point-details {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        border-left: 3px solid #4ecdc4;
        color: #ffffff;
      }

      .point-details p {
        color: #e0e0e0;
        line-height: 1.6;
      }

      .point-details strong {
        color: #4ecdc4;
      }

      .point-details ul {
        color: #e0e0e0;
      }

      .point-details li {
        color: #d0d0d0;
        margin-bottom: 5px;
      }

      .timeline-point.turning-point .point-details {
        border-left-color: #ffd700;
        background: rgba(255, 215, 0, 0.1);
      }

      .chapter-info {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
      }

      .presence-level {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        text-transform: capitalize;
      }

      .presence-level.major {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
      }

      .presence-level.supporting {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
      }

      .presence-level.mentioned {
        background: rgba(158, 158, 158, 0.2);
        color: #9e9e9e;
      }

      .emotional-state {
        margin: 8px 0;
      }

      .emotion-label {
        color: #aaa;
        margin-right: 8px;
      }

      .emotion-value {
        color: #4ecdc4;
        font-weight: bold;
      }

      .key-actions {
        margin: 10px 0;
      }

      .actions-label {
        color: #aaa;
        display: block;
        margin-bottom: 5px;
      }

      .key-actions ul {
        margin: 0;
        padding-left: 20px;
        color: #ddd;
      }

      .turning-point-details {
        background: rgba(255, 215, 0, 0.1);
        border-radius: 8px;
        padding: 12px;
        margin: 10px 0;
        border: 1px solid rgba(255, 215, 0, 0.3);
        color: #ffffff;
      }

      .event-description, .impact-description {
        margin: 8px 0;
        color: #e0e0e0;
      }

      .event-description strong, .impact-description strong {
        color: #ffd700;
      }

      .development-notes {
        margin-top: 10px;
        color: #aaa;
        font-style: italic;
      }

      /* Turning Points Analysis */
      .turning-points-analysis {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
        margin: 20px 0;
      }

      .turning-points-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .turning-point-card {
        background: rgba(255, 215, 0, 0.1);
        border-radius: 8px;
        padding: 20px;
        border: 1px solid rgba(255, 215, 0, 0.3);
        transition: all 0.3s ease;
      }

      .turning-point-card:hover {
        background: rgba(255, 215, 0, 0.15);
        transform: translateY(-2px);
      }

      .tp-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
      }

      .tp-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #ffd700;
        color: #1a1a2e;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }

      .tp-chapter {
        color: #ffd700;
        font-weight: bold;
      }

      .tp-content h5 {
        margin: 0 0 10px 0;
        color: #fff;
      }

      .tp-impact {
        margin: 0;
        color: #ddd;
        line-height: 1.5;
      }

      /* Emotional Journey Styles */
      .emotional-journey {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
      }

      .emotional-timeline {
        margin: 20px 0;
      }

      .emotion-track {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        justify-content: center;
      }

      .emotion-point {
        text-align: center;
        min-width: 120px;
      }

      .emotion-marker {
        margin-bottom: 10px;
      }

      .chapter-num {
        display: block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #4ecdc4;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 10px auto;
        font-weight: bold;
      }

      .emotion-state {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: bold;
        text-transform: capitalize;
      }

      .emotion-state.positive {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
      }

      .emotion-state.negative {
        background: rgba(244, 67, 54, 0.2);
        color: #f44336;
      }

      .emotion-state.neutral {
        background: rgba(158, 158, 158, 0.2);
        color: #9e9e9e;
      }

      .emotion-details {
        margin-top: 10px;
        text-align: left;
        color: #e0e0e0;
      }

      .emotion-details strong {
        color: #4ecdc4;
      }

      .emotion-details ul {
        color: #d0d0d0;
      }

      .emotion-details li {
        color: #d0d0d0;
      }

      .motivations-analysis {
        margin-top: 30px;
      }

      .motivations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .motivation-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #e0e0e0;
      }

      .motivation-icon {
        font-size: 1.5em;
      }

      /* Comparison View Styles */
      .character-comparison {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
      }

      .comparison-grid {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 30px;
        align-items: start;
        margin: 20px 0;
      }

      .comparison-character {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 20px;
      }

      .comparison-character h5 {
        color: #4ecdc4;
        margin-bottom: 15px;
        text-align: center;
        font-size: 1.2em;
      }

      .comparison-vs {
        font-size: 2em;
        font-weight: bold;
        color: #ffd700;
        text-align: center;
        align-self: center;
      }

      .comparison-metrics {
        display: grid;
        gap: 12px;
      }

      .metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .metric-label {
        color: #aaa;
        font-size: 0.9em;
      }

      .metric-value {
        font-weight: bold;
        color: #fff;
      }

      .metric-value.positive {
        color: #4caf50;
      }

      .metric-value.negative {
        color: #f44336;
      }

      .metric-value.complex {
        color: #ff9800;
      }

      .metric-value.overall {
        color: #4ecdc4;
      }

      .evolution-comparison {
        margin-top: 25px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 8px;
        padding: 20px;
      }

      .evolution-comparison h5 {
        color: #ffd700;
        margin-bottom: 15px;
        text-align: center;
      }

      .pattern-comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }

      .pattern-item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
      }

      .pattern-item h6 {
        color: #4ecdc4;
        margin-bottom: 10px;
      }

      .pattern-item p {
        margin: 5px 0;
        color: #ddd;
        line-height: 1.4;
      }

      /* Growth Patterns Styles */
      .growth-patterns {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 25px;
      }

      .patterns-overview {
        margin-top: 20px;
      }

      .pattern-group {
        margin-bottom: 25px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 8px;
        padding: 20px;
      }

      .trajectory-header {
        margin-bottom: 15px;
        font-size: 1.2em;
        font-weight: bold;
        text-transform: uppercase;
      }

      .pattern-characters {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin-top: 15px;
      }

      .pattern-character-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .pattern-character-card:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
      }

      .pattern-character-card .char-name {
        font-weight: bold;
        color: #fff;
        margin-bottom: 3px;
      }

      .pattern-character-card .char-archetype {
        color: #ffd700;
        font-size: 0.85em;
        margin-bottom: 3px;
      }

      .pattern-character-card .turning-points {
        color: #4ecdc4;
        font-size: 0.8em;
      }

      /* Placeholder Styles */
      .placeholder, .comparison-placeholder {
        text-align: center;
        padding: 60px 20px;
        color: #aaa;
      }

      .placeholder-icon {
        font-size: 4em;
        margin-bottom: 20px;
      }

      .placeholder h3 {
        color: #4ecdc4;
        margin: 20px 0 10px 0;
      }

      /* Interactive Elements */
      .timeline-point {
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .timeline-point:hover .point-details {
        background: rgba(255, 255, 255, 0.08);
        border-left-color: #ffd700;
      }

      .explore-moment {
        transition: all 0.3s ease;
      }

      .explore-moment:hover {
        background: linear-gradient(135deg, #44a08d, #4ecdc4);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .character-selectors {
          flex-direction: column;
          gap: 15px;
        }

        .view-mode-tabs {
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
        }

        .comparison-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .comparison-vs {
          order: -1;
          font-size: 1.5em;
        }

        .pattern-comparison {
          grid-template-columns: 1fr;
        }

        .pattern-characters {
          grid-template-columns: 1fr;
        }
      }
    `;
    document.head.appendChild(styles);
  }
}

// Create and export singleton instance
export const characterEvolution = new CharacterEvolutionVisualization();
