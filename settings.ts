import { createNodeCompoundProgram } from 'sigma/rendering/webgl/programs/common/node';
import NodePointProgram from 'sigma/rendering/webgl/programs/node.point';
import NodePointWithBorderProgram from '@yomguithereal/sigma-experiments-renderers/node/node.point.border';
import NodeHaloProgram from "./node.halo";
import getNodeProgramImage from "./node.image";
import { drawLabel, drawHover } from './node.label';


const startYear = 1939,
  curYear = (new Date).getFullYear(),
  totalYears = curYear - startYear + 1,
  picturesLoadingDelay = 1500,
  playComicsDelay = 1500,
  creatorsRoles = {
    writer: "#234fac",
    artist: "#2b6718",
    both: "#d4a129"
  },
  clusters = {
    creators: {
      "Silver Age": {
        match: ["<PERSON> Lee", "<PERSON>", "<PERSON>"],
        color: "#DDDDDD"
      },
      "Bronze Age": {
        match: ["<PERSON>", "<PERSON>", "<PERSON>"],
        color: "#ff993e"
      },
      "Modern Age": {
        match: ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        color: "#bce25b"
      },
      "<PERSON><PERSON>um Age": {
        match: ["<PERSON>", "<PERSON> <PERSON> Bendis", "Dan Slott"],
        color: "#5fb1ff"
      }
    },
    characters: {
      // Harry Potter House Colors and Character Groups - Bright Colors
      "Gryffindor": {
        match: ["Harry Potter", "Hermione Granger", "<PERSON> We<PERSON>ley", "Ginny Weasley", "Neville Longbottom"],
        color: "#ffd700"  // Bright Gold
      },
      "Slytherin": {
        match: ["Draco Malfoy", "Severus Snape", "Lord Voldemort", "Bellatrix Lestrange", "Lucius Malfoy"],
        color: "#32cd32"  // Lime Green
      },
      "Death Eaters": {
        match: ["Death Eater", "Yaxley", "Wormtail", "Fenrir Greyback"],
        color: "#696969"  // Dim Gray (more visible than black)
      },
      "Order of Phoenix": {
        match: ["Remus Lupin", "Kingsley Shacklebolt", "Mad-Eye Moody", "Tonks"],
        color: "#ff4500"  // Orange Red
      },
      "Ministry Officials": {
        match: ["Pius Thicknesse", "Scrimgeour", "Umbridge"],
        color: "#9370db"  // Medium Purple
      },
      "Hogwarts Staff": {
        match: ["Dumbledore", "McGonagall", "Charity Burbage"],
        color: "#da70d6"  // Orchid
      },
      "Magical Creatures": {
        match: ["Nagini", "Griphook", "Dobby"],
        color: "#00ff7f"  // Spring Green
      },
      "Other Characters": {
        match: ["Mundungus Fletcher", "Xenophilius Lovegood"],
        color: "#daa520"  // Goldenrod
      }
    }
  },
  extraPalette = [
    "#ffd700",  // Bright Gold (Gryffindor)
    "#32cd32",  // Lime Green (Slytherin)
    "#ff4500",  // Orange Red (Bright Red)
    "#9370db",  // Medium Purple (Bright Indigo)
    "#da70d6",  // Orchid (Bright Purple)
    "#00ff7f",  // Spring Green (Bright Forest Green)
    "#daa520",  // Goldenrod (Bright Peru/Brown)
    "#696969",  // Dim Gray (Lighter than black)
    "#ffa500",  // Orange (Bright Goldenrod)
    "#9acd32"   // Yellow Green (Bright Olive)
  ],
  smallScreen = Math.min(window.innerWidth, window.innerHeight) < 600,
  NodeProgramImage = getNodeProgramImage(smallScreen ? 96 : 192),
  sigmaSettings = {
    maxCameraRatio: 75,
    defaultEdgeColor: '#ffffff',  // White edges for better visibility on dark background
    defaultEdgeType: 'line',
    edgeLabelFont: '"DejaVu Sans Mono", "DejaVuSansMono", monospace',
    edgeLabelSize: 10,
    edgeLabelColor: {color: '#ffffff'},
    labelRenderer: drawLabel,
    labelFont: '"DejaVu Sans Mono", "DejaVuSansMono", monospace',
    labelColor: {color: '#AAA'},
    labelWeight: 'bold',
    labelDensity: 2, // Higher density to show more labels
    labelGridCellSize: 300,
    hoverRenderer: drawHover,
    zoomToSizeRatioFunction: ratio => Math.pow(ratio, 0.75),
    nodeProgramClasses: {
      circle: createNodeCompoundProgram([
        NodeHaloProgram,
        NodePointProgram
      ]),
      image: createNodeCompoundProgram([
        NodeHaloProgram,
        NodePointWithBorderProgram,
        NodeProgramImage
      ])
    },
    nodeHoverProgramClasses: {
      circle: createNodeCompoundProgram([
        NodePointProgram
      ]),
      image: createNodeCompoundProgram([
        NodePointWithBorderProgram,
        NodeProgramImage
      ])
    }
  };

export {
  startYear, curYear, totalYears,
  picturesLoadingDelay, playComicsDelay,
  creatorsRoles, clusters,
  extraPalette,
  sigmaSettings
};
