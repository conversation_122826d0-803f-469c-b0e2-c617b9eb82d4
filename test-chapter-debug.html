<!DOCTYPE html>
<html>
<head>
    <title>Chapter Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        #console-output { 
            background: #f0f0f0; 
            padding: 10px; 
            height: 300px; 
            overflow-y: scroll; 
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Chapter Loading Debug Test</h1>
    
    <div class="debug-section">
        <h3>Test Chapter Data Loading</h3>
        <button onclick="testChapterLoad(1)">Test Chapter 1</button>
        <button onclick="testChapterLoad(2)">Test Chapter 2</button>
        <button onclick="testChapterLoad(3)">Test Chapter 3</button>
        <button onclick="testGlobalLoad()">Test Global</button>
        <button onclick="clearConsole()">Clear Console</button>
    </div>
    
    <div class="debug-section">
        <h3>Console Output</h3>
        <div id="console-output"></div>
    </div>

    <script src="pako_inflate.min.js"></script>
    <script>
        // Capture console logs
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function logToDiv(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToDiv(args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToDiv(args.join(' '), 'error');
        };
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        // Test functions
        async function testChapterLoad(chapterNum) {
            console.log(`🔄 Testing chapter ${chapterNum} load...`);

            try {
                const filename = `./data/HarryPotter_characters_by_stories_c${chapterNum}.json.gz`;
                console.log(`📁 Fetching: ${filename}`);

                const response = await fetch(filename);
                console.log(`📡 Response status: ${response.status} ${response.ok ? 'OK' : 'FAILED'}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const arrayBuffer = await response.arrayBuffer();
                console.log(`📦 Data received: ${arrayBuffer.byteLength} bytes`);

                // Test both compression methods
                try {
                    const decompressed = pako.inflate(arrayBuffer, { to: 'string' });
                    console.log(`✅ Decompressed with inflate: ${decompressed.length} characters`);

                    const parsed = JSON.parse(decompressed);
                    console.log(`📊 Parsed data: ${parsed.nodes.length} nodes, ${parsed.edges.length} edges`);

                    // Show first few character names and their attributes
                    const chars = parsed.nodes.slice(0, 3).map(n => {
                        const attrs = n.attributes;
                        return `${attrs.name} (${attrs.importance_score || attrs.stories || 'no score'})`;
                    }).join(', ');
                    console.log(`👥 Characters: ${chars}...`);

                    // Check data structure
                    const firstNode = parsed.nodes[0];
                    console.log(`🔍 First node structure:`, Object.keys(firstNode.attributes));

                } catch (inflateError) {
                    console.log(`⚠️ Inflate failed: ${inflateError.message}`);
                    console.log(`🔄 Trying ungzip...`);

                    const decompressed = pako.ungzip(arrayBuffer, { to: 'string' });
                    console.log(`✅ Decompressed with ungzip: ${decompressed.length} characters`);

                    const parsed = JSON.parse(decompressed);
                    console.log(`📊 Parsed data: ${parsed.nodes.length} nodes, ${parsed.edges.length} edges`);
                }

            } catch (error) {
                console.error(`Failed to load chapter ${chapterNum}:`, error.message);
            }
        }
        
        async function testGlobalLoad() {
            console.log(`🌍 Testing global data load...`);

            try {
                const filename = `./data/HarryPotter_characters_by_stories_full_processed.json.gz`;
                console.log(`📁 Fetching: ${filename}`);

                const response = await fetch(filename);
                console.log(`📡 Response status: ${response.status} ${response.ok ? 'OK' : 'FAILED'}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const arrayBuffer = await response.arrayBuffer();
                console.log(`📦 Data received: ${arrayBuffer.byteLength} bytes`);

                // Test both compression methods for global file too
                try {
                    const decompressed = pako.inflate(arrayBuffer, { to: 'string' });
                    console.log(`✅ Decompressed with inflate: ${decompressed.length} characters`);

                    const parsed = JSON.parse(decompressed);
                    console.log(`📊 Parsed data: ${parsed.nodes.length} nodes, ${parsed.edges.length} edges`);

                    // Show first few character names and their attributes
                    const chars = parsed.nodes.slice(0, 3).map(n => {
                        const attrs = n.attributes;
                        return `${attrs.name} (${attrs.stories || attrs.importance_score || 'no score'})`;
                    }).join(', ');
                    console.log(`👥 Characters: ${chars}...`);

                    // Check data structure
                    const firstNode = parsed.nodes[0];
                    console.log(`🔍 First node structure:`, Object.keys(firstNode.attributes));

                } catch (inflateError) {
                    console.log(`⚠️ Inflate failed: ${inflateError.message}`);
                    console.log(`🔄 Trying ungzip...`);

                    const decompressed = pako.ungzip(arrayBuffer, { to: 'string' });
                    console.log(`✅ Decompressed with ungzip: ${decompressed.length} characters`);

                    const parsed = JSON.parse(decompressed);
                    console.log(`📊 Parsed data: ${parsed.nodes.length} nodes, ${parsed.edges.length} edges`);
                }

            } catch (error) {
                console.error(`Failed to load global data:`, error.message);
            }
        }
        
        console.log('🚀 Chapter debug test page loaded');
    </script>
</body>
</html>
