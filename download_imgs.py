import urllib.request
import os

image_urls = [
    "https://ik.imagekit.io/hpapi/harry.jpg",
    "https://ik.imagekit.io/hpapi/hermione.jpeg",
    "https://ik.imagekit.io/hpapi/ron.jpg",
    "https://ik.imagekit.io/hpapi/draco.jpg",
    "https://ik.imagekit.io/hpapi/snape.jpg",
    "https://ik.imagekit.io/hpapi/dumbledore.jpg",
    "https://ik.imagekit.io/hpapi/hagrid.jpg",
    "https://ik.imagekit.io/hpapi/mcgonagall.jpg",
    "https://ik.imagekit.io/hpapi/voldemort.jpg",
    "https://ik.imagekit.io/hpapi/sirius.jpg",
    "https://ik.imagekit.io/hpapi/dobby.jpg",
    "https://ik.imagekit.io/hpapi/bellatrix.jpg",
    "https://ik.imagekit.io/hpapi/luna.jpg",
    "https://ik.imagekit.io/hpapi/neville.jpg",
    "https://ik.imagekit.io/hpapi/ginny.jpg",
    "https://ik.imagekit.io/hpapi/fred.jpg",
    "https://ik.imagekit.io/hpapi/george.jpg",
    "https://ik.imagekit.io/hpapi/malfoy.jpg",
    "https://ik.imagekit.io/hpapi/lupin.jpg",
    "https://ik.imagekit.io/hpapi/tonks.jpg",
    "https://ik.imagekit.io/hpapi/cedric.jpg",
    "https://ik.imagekit.io/hpapi/cho.jpg",
    "https://ik.imagekit.io/hpapi/fleur.jpg",
    "https://ik.imagekit.io/hpapi/krum.jpg",
    "https://ik.imagekit.io/hpapi/umbridge.jpg"
]

for url in image_urls:
    try:
        # Extract filename from URL
        filename = os.path.basename(url)
        # Download and save the image
        urllib.request.urlretrieve(url, filename)
        print(f"Downloaded {filename}")
    except Exception as e:
        print(f"Failed to download {url}: {e}")