const fs = require('fs');
const pako = require('pako');

console.log("🔍 Testing Global Data Loading");

// Test loading the global data file
try {
  const globalData = fs.readFileSync('data/HarryPotter_characters_by_stories_full_processed.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  
  console.log(`✅ Global data loaded successfully`);
  console.log(`📊 Global View: ${globalGraph.nodes.length} characters, ${globalGraph.edges.length} relationships`);
  
  // Show some sample characters
  console.log('\n🎭 Sample Characters:');
  globalGraph.nodes.slice(0, 5).forEach(node => {
    console.log(`  - ${node.attributes.name} (importance: ${node.attributes.overall_importance}, chapters: ${node.attributes.stories})`);
  });
  
  // Show some sample relationships
  console.log('\n🔗 Sample Relationships:');
  globalGraph.edges.slice(0, 3).forEach(edge => {
    const sourceNode = globalGraph.nodes.find(n => n.key === edge.source);
    const targetNode = globalGraph.nodes.find(n => n.key === edge.target);
    console.log(`  - ${sourceNode.attributes.name} ↔ ${targetNode.attributes.name} (${edge.attributes.relationship_type})`);
  });
  
  // Check if chapters attribute exists
  console.log('\n📖 Chapter Information:');
  globalGraph.nodes.forEach(node => {
    if (node.attributes.chapters && node.attributes.chapters.length > 0) {
      console.log(`  - ${node.attributes.name}: chapters ${node.attributes.chapters.join(', ')}`);
    }
  });
  
} catch (error) {
  console.error("❌ Error loading global data:", error.message);
}

// Test loading chapter metadata
try {
  const chapterMetadata = JSON.parse(fs.readFileSync('data/HarryPotter_chapters_metadata.json', 'utf8'));
  console.log(`\n📚 Chapter Metadata: ${Object.keys(chapterMetadata).length} chapters`);
  Object.keys(chapterMetadata).forEach(chapterNum => {
    const chapter = chapterMetadata[chapterNum];
    console.log(`  - Chapter ${chapterNum}: ${chapter.chapter_title}`);
  });
} catch (error) {
  console.error("❌ Error loading chapter metadata:", error.message);
}

console.log("\n✅ Global data test complete!");
