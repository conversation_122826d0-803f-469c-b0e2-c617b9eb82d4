const fs = require('fs');
const pako = require("pako");

const graphology = require("graphology");
const layouts = require("graphology-layout");
const forceAtlas2 = require('graphology-layout-forceatlas2');
const noverlap = require('graphology-layout-noverlap');
const louvain = require('graphology-communities-louvain');

const args = process.argv.slice(2);
const filename = args[0] || 'data/HarryPotter_characters_by_stories_full.json';
const FA2Iterations = args.length > 1 ? parseInt(args[1]) : 3000;
const batchIterations = args.length > 2 ? parseInt(args[2]) : 500;

const entity = "characters"; // Harry <PERSON> only has characters
const links_type = "stories";

function readJSON(filename) {
  console.log("Working on " + filename + " ...");
  const jsonfile = fs.readFileSync(filename, {encoding:'utf8', flag:'r'});
  const data = JSON.parse(jsonfile);
  
  // Convert from our format to graphology format
  const graph = new graphology.Graph({type: 'undirected'});
  
  // Add nodes
  data.nodes.forEach(node => {
    graph.addNode(node.key, node.attributes);
  });
  
  // Add edges
  data.edges.forEach(edge => {
    if (graph.hasNode(edge.source) && graph.hasNode(edge.target)) {
      graph.addEdge(edge.source, edge.target, edge.attributes);
    }
  });

  // Set initial circular positions
  const circularPositions = layouts.circular(graph, { scale: 50 });

  graph.forEachNode(node => {
    const size = graph.getNodeAttribute(node, links_type) || 1;
    graph.mergeNodeAttributes(node, {
      x: circularPositions[node].x,
      y: circularPositions[node].y,
      size: Math.pow(size, 0.2) * 1.75 * 1.25
    });
  });

  return graph;
}

function runBatchFA2(graph, settings, doneIterations, callback) {
  if (doneIterations >= FA2Iterations) {
    return callback(doneIterations);
  }
  
  const iterations = Math.min(batchIterations, FA2Iterations - doneIterations);
  console.log(`Running ForceAtlas2 batch: ${doneIterations + 1}-${doneIterations + iterations} / ${FA2Iterations}`);
  
  forceAtlas2.assign(graph, {
    iterations: iterations,
    getEdgeWeight: "weight",
    settings: settings
  });
  
  setTimeout(() => {
    runBatchFA2(graph, settings, doneIterations + iterations, callback);
  }, 0);
}

function processGraph(graph) {
  // Displaying graph's stats
  console.log('Number of nodes:', graph.order);
  console.log('Number of edges:', graph.size);

  let time0 = Date.now();

  // Use pointwise mutual information to sparse edges
  const total = graph.reduceNodes((tot, node, attrs) => tot + (attrs[links_type] || 1), 0);
  graph.forEachEdge((edge, attrs, n1, n2, n1_attrs, n2_attrs) => {
    const weight = attrs.weight || 1;
    const n1_stories = n1_attrs[links_type] || 1;
    const n2_stories = n2_attrs[links_type] || 1;
    
    const pmi = Math.max(
      graph.degree(n1) === 1 || graph.degree(n2) === 1 ? 1 : 0, 
      Math.log(total * weight / (n1_stories * n2_stories))
    );
    
    graph.setEdgeAttribute(edge, "weight", pmi);
  });

  // Run Louvain to find communities for characters
  console.log('Running Louvain community detection...');
  louvain.assign(graph, {resolution: 1.2});

  // Spatializing with FA2
  console.log('Starting ForceAtlas2 for ' + FA2Iterations + ' iterations by batches of ' + batchIterations);
  const settings = forceAtlas2.inferSettings(graph);
  settings.edgeWeightInfluence = 0.5;
  
  runBatchFA2(graph, settings, 0, function(doneIterations) {
    let time1 = Date.now();
    console.log('ForceAtlas2 fully processed in:', (time1 - time0)/1000 + "s (" + doneIterations + " iterations)");

    // Apply noverlap to prevent node overlaps
    console.log('Applying noverlap...');
    noverlap.assign(graph);

    // Set colors based on communities
    const communities = {};
    const colors = [
      '#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', 
      '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
    ];
    
    graph.forEachNode((node, attrs) => {
      const community = attrs.community || 0;
      if (!communities[community]) {
        communities[community] = colors[Object.keys(communities).length % colors.length];
      }
      graph.setNodeAttribute(node, 'color', communities[community]);
    });

    // Reduce output size by reducing floats to ints for weights
    graph.forEachEdge((edge, {weight}) =>
      graph.setEdgeAttribute(edge, "weight", Math.round(1000 * weight))
    );

    // Save the processed graph
    const outputFile = filename.replace('.json', '_processed.json.gz');
    const graphData = JSON.stringify(graph);
    fs.writeFileSync(outputFile, pako.gzip(graphData));
    console.log(" -> Saved " + outputFile);
    
    // Also save uncompressed for debugging
    const debugFile = filename.replace('.json', '_processed.json');
    fs.writeFileSync(debugFile, graphData);
    console.log(" -> Saved " + debugFile);
  });
}

// Main execution
if (require.main === module) {
  if (!fs.existsSync(filename)) {
    console.error("File not found:", filename);
    process.exit(1);
  }
  
  const graph = readJSON(filename);
  processGraph(graph);
}

module.exports = { readJSON, processGraph };
