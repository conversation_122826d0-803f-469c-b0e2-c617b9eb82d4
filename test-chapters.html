<!DOCTYPE html>
<html>
<head>
    <title>Chapter Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chapter-controls { margin: 20px 0; }
        .chapter-btn { 
            margin: 5px; 
            padding: 10px 15px; 
            background: #3498db; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .chapter-btn:hover { background: #2980b9; }
        .chapter-btn.active { background: #e74c3c; }
        .status { 
            margin: 10px 0; 
            padding: 10px; 
            background: #f8f9fa; 
            border-left: 4px solid #007bff; 
        }
    </style>
</head>
<body>
    <h1>Harry <PERSON> Chapter Testing</h1>
    
    <div class="status" id="status">
        Loading...
    </div>
    
    <div class="chapter-controls">
        <button class="chapter-btn" onclick="testGlobalView()">Global View</button>
        <button class="chapter-btn" onclick="testChapter(1)">Chapter 1</button>
        <button class="chapter-btn" onclick="testChapter(2)">Chapter 2</button>
        <button class="chapter-btn" onclick="testChapter(3)">Chapter 3</button>
        <button class="chapter-btn" onclick="testChapter(4)">Chapter 4</button>
        <button class="chapter-btn" onclick="testChapter(5)">Chapter 5</button>
        <button class="chapter-btn" onclick="testChapter(6)">Chapter 6</button>
        <button class="chapter-btn" onclick="testChapter(7)">Chapter 7</button>
        <button class="chapter-btn" onclick="testChapter(8)">Chapter 8</button>
    </div>
    
    <div id="graph-info">
        <h3>Graph Information</h3>
        <div id="graph-details"></div>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function updateGraphInfo() {
            const details = document.getElementById('graph-details');
            
            // Check if the main app is loaded
            if (typeof window.getCurrentChapter === 'function') {
                const currentChapter = window.getCurrentChapter();
                const chapterText = currentChapter === null ? 'Global View' : `Chapter ${currentChapter}`;
                
                details.innerHTML = `
                    <p><strong>Current View:</strong> ${chapterText}</p>
                    <p><strong>Available Chapters:</strong> ${window.availableChapters ? window.availableChapters.join(', ') : 'Loading...'}</p>
                `;
                
                // Update button states
                document.querySelectorAll('.chapter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                if (currentChapter === null) {
                    document.querySelector('button[onclick="testGlobalView()"]').classList.add('active');
                } else {
                    document.querySelector(`button[onclick="testChapter(${currentChapter})"]`).classList.add('active');
                }
            } else {
                details.innerHTML = '<p>Main application not loaded yet...</p>';
            }
        }
        
        function testGlobalView() {
            updateStatus('Switching to Global View...');
            if (typeof window.switchToGlobalView === 'function') {
                window.switchToGlobalView();
                setTimeout(() => {
                    updateStatus('Switched to Global View');
                    updateGraphInfo();
                }, 1000);
            } else {
                updateStatus('Error: switchToGlobalView function not available');
            }
        }
        
        function testChapter(chapterNum) {
            updateStatus(`Switching to Chapter ${chapterNum}...`);
            if (typeof window.switchToChapter === 'function') {
                window.switchToChapter(chapterNum);
                setTimeout(() => {
                    updateStatus(`Switched to Chapter ${chapterNum}`);
                    updateGraphInfo();
                }, 1000);
            } else {
                updateStatus('Error: switchToChapter function not available');
            }
        }
        
        // Check for functions periodically
        function checkFunctions() {
            if (typeof window.getCurrentChapter === 'function') {
                updateStatus('Chapter functions loaded successfully!');
                updateGraphInfo();
            } else {
                updateStatus('Waiting for main application to load...');
                setTimeout(checkFunctions, 1000);
            }
        }
        
        // Start checking
        checkFunctions();
        
        // Update graph info periodically
        setInterval(updateGraphInfo, 2000);
    </script>
</body>
</html>
