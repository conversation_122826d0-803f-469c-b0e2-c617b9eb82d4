const fs = require('fs');
const pako = require('pako');
const Graph = require('graphology');
const forceAtlas2 = require('graphology-layout-forceatlas2');
const noverlap = require('graphology-layout-noverlap');

function spatializeHPGlobal(filename) {
  console.log(`Reading ${filename} ...`);
  
  // Read and decompress the file
  const data = fs.readFileSync(filename);
  const jsonString = pako.inflate(data, {to: 'string'});
  const graphData = JSON.parse(jsonString);
  
  console.log(`Spatializing ${filename} with ${graphData.nodes.length} nodes and ${graphData.edges.length} edges...`);
  
  // Create a Graphology graph
  const graph = Graph.from(graphData);
  
  // Set initial random positions if not present
  graph.forEachNode((node, attributes) => {
    if (attributes.x === undefined || attributes.y === undefined) {
      graph.setNodeAttribute(node, 'x', Math.random() * 1000 - 500);
      graph.setNodeAttribute(node, 'y', Math.random() * 1000 - 500);
    }
  });
  
  // Apply ForceAtlas2 layout
  const settings = {
    iterations: 500,
    settings: {
      scalingRatio: 10,
      gravity: 0.1,
      strongGravityMode: false,
      barnesHutOptimize: true,
      barnesHutTheta: 0.5
    }
  };

  forceAtlas2.assign(graph, settings);
  
  // Apply noverlap to prevent node overlapping
  const noverlapSettings = {
    maxIterations: 100,
    settings: {
      margin: 20,
      expansion: 1.1,
      gridSize: 20,
      speed: 3
    }
  };
  
  noverlap.assign(graph, noverlapSettings);
  
  // Convert back to the original format
  const spatializedData = {
    attributes: graphData.attributes,
    options: graphData.options,
    nodes: [],
    edges: []
  };
  
  graph.forEachNode((node, attributes) => {
    spatializedData.nodes.push({
      key: node,
      attributes: attributes
    });
  });
  
  graph.forEachEdge((edge, attributes, source, target) => {
    spatializedData.edges.push({
      key: edge,
      source: source,
      target: target,
      attributes: attributes
    });
  });
  
  // Write back to file
  console.log(`Writing ${filename} ...`);
  const compressedData = pako.deflate(JSON.stringify(spatializedData, null, 0));
  fs.writeFileSync(filename, compressedData);
}

// Process the global HP file
const globalFilename = 'data/HarryPotter_characters_by_stories_full_processed.json.gz';
if (fs.existsSync(globalFilename)) {
  spatializeHPGlobal(globalFilename);
} else {
  console.log(`Warning: ${globalFilename} not found, skipping...`);
}

console.log("HP global network spatialized!");
