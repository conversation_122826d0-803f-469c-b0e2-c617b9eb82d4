{"attributes": {}, "options": {"allowSelfLoops": false, "multi": false, "type": "undirected"}, "nodes": [{"key": "harry_potter", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 12, "chapter_importance": 10, "presence_level": "protagonist", "emotional_state": "conflicted", "actions": ["Wakes up feeling lonely and resentful towards <PERSON><PERSON><PERSON><PERSON>.", "Explores the upper floors of Grimmauld Place and enters Sirius's bedroom.", "Finds and reads a letter from his mother, <PERSON>, and a torn photograph of himself as a baby.", "Identifies <PERSON><PERSON>tur<PERSON> as R.A.B.", "Su<PERSON>ons and interrogates <PERSON><PERSON><PERSON> about the locket.", "Stops <PERSON><PERSON><PERSON> from punishing himself.", "Gives <PERSON><PERSON><PERSON> a new mission to find <PERSON>nd<PERSON><PERSON> Fletcher.", "Gives <PERSON><PERSON><PERSON> the fake locket as a token of gratitude."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON> is confronted with the severe costs of war, losing friends and mentors. He solidifies his moral stance against killing, even when criticized by allies. His assertion of trust in the face of possible betrayal shows a maturation, though <PERSON><PERSON> sees it as a dangerous echo of his father's tragic flaw. The return of his scar-link to <PERSON><PERSON><PERSON><PERSON> re-establishes a critical vulnerability.", "community": 1, "url": "#character/harry_potter", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "hermione_granger", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 9, "chapter_importance": 9, "presence_level": "major", "emotional_state": "empathetic", "actions": ["Wakes up and finds <PERSON>, expressing her fear when he disappeared.", "Realizes that the locket <PERSON><PERSON><PERSON><PERSON> was the one they threw out in the drawing room cabinet.", "Attempts to summon the locket with '<PERSON><PERSON>o'.", "Shows immense compassion and empathy for <PERSON><PERSON><PERSON>, crying during his story and trying to hug him.", "Explains the nuances of house-elf magic and loyalty to <PERSON> and <PERSON>.", "Urges <PERSON> to stop <PERSON><PERSON><PERSON> from punishing himself."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s character is defined by immense sacrifice and foresight in this chapter. Her decision to erase her parents' memories is a shocking display of maturity and dedication to the cause, establishing her as an equal partner in the mission's burdens, not just a follower.", "community": 4, "url": "#character/hermione_granger", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "ron_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 9, "chapter_importance": 7, "presence_level": "major", "emotional_state": "supportive", "actions": ["Insists <PERSON><PERSON><PERSON> sleep on the sofa cushions.", "Gets annoyed when <PERSON> disappears without telling them.", "Helps search <PERSON><PERSON>'s room for the locket.", "Correctly theorizes that <PERSON><PERSON><PERSON> could Disapparate from the cave because elf magic is different from wizard's magic.", "Looks troubled by <PERSON><PERSON><PERSON>'s story.", "Calls <PERSON>'s gift of the locket to <PERSON><PERSON><PERSON> 'overkill'."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON> demonstrates immense loyalty and maturity in his desire to protect <PERSON><PERSON><PERSON> from the new anti-Muggle-born laws. While still restless and prone to bickering, his core values of friendship and protection are strongly affirmed. He provides a more balanced perspective on the Lupin conflict than either <PERSON> or <PERSON><PERSON><PERSON>.", "community": 4, "url": "#character/ron_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "kreacher", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 3, "chapter_importance": 10, "presence_level": "major", "emotional_state": "grief-stricken", "actions": ["Appears when summoned by <PERSON>.", "Confesses that he took the locket back after the Order threw it out.", "Reveals that <PERSON><PERSON><PERSON><PERSON> stole the locket and other Black family heirlooms.", "Tells the full, tragic story of his journey to the sea cave with <PERSON><PERSON><PERSON><PERSON>.", "Recounts how <PERSON><PERSON> sacrificed himself to retrieve the Horcrux and ordered <PERSON><PERSON><PERSON> to destroy it.", "Attempts to punish himself with a poker and by banging his head on the floor.", "Accepts a new mission from <PERSON> to find <PERSON><PERSON><PERSON><PERSON>.", "Is overcome with emotion upon receiving <PERSON><PERSON>'s locket from <PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s transformation is complete. He is now diligently and loyally serving <PERSON>, successfully completing a difficult mission. His violent hatred for <PERSON><PERSON><PERSON><PERSON> is channeled into his service, and he is happy to receive praise from <PERSON>.", "community": 0, "url": "#character/kreacher", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "regulus_arcturus_black", "attributes": {"label": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "stories": 1, "chapter_importance": 9, "presence_level": "major", "emotional_state": "determined", "actions": ["(Posthumously) Joined the Death Eaters as a teenager.", "(Posthumously) Volunteer<PERSON> <PERSON><PERSON><PERSON> to assist <PERSON><PERSON><PERSON><PERSON>.", "(Posthumously) <PERSON><PERSON> disenchanted and decided to bring <PERSON><PERSON><PERSON><PERSON> down.", "(Posthumously) Ordered <PERSON><PERSON><PERSON> to take him to the Horcrux cave.", "(Posthumously) <PERSON><PERSON><PERSON> the emerald potion himself, swapped the real locket with a fake one, and ordered <PERSON><PERSON><PERSON> to leave him to die and destroy the Horcrux."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Revealed posthumously as a complex and heroic character. He changed from a Voldemort supporter to someone who sacrificed his own life in an attempt to defeat him, motivated by a change of heart and care for his house-elf.", "community": 0, "url": "#character/regulus_arcturus_black", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "sirius_black", "attributes": {"label": "Sirius Black", "name": "Sirius Black", "stories": 2, "chapter_importance": 7, "presence_level": "mentioned", "emotional_state": "rebellious", "actions": ["(Posthumously) Decorated his teenage bedroom to annoy his parents with Gryffindor banners and Muggle pictures.", "(Posthumously) Used a Permanent Sticking Charm on his wall decorations.", "(Posthumously) Bought <PERSON> his first toy broomstick for his first birthday.", "(Posthumously) Was horrible to <PERSON><PERSON><PERSON>, which <PERSON><PERSON><PERSON> identifies as a contributing factor to his death."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "His character is fleshed out through his teenage bedroom and <PERSON>'s letter, showing his rebellious but loving nature. However, his flaws, particularly his cruelty to <PERSON><PERSON><PERSON>, are highlighted as having had fatal consequences.", "community": 0, "url": "#character/sirius_black", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "lily_potter", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 1, "chapter_importance": 8, "presence_level": "mentioned", "emotional_state": "loving", "actions": ["(Posthumously) Wrote a letter to <PERSON> Black, thanking him for <PERSON>'s first birthday present.", "(Posthumously) Described baby <PERSON>'s antics on his toy broomstick.", "(Posthumously) Mentioned visits from Bathilda Bagshot and <PERSON>.", "(Posthumously) Noted that Dumbledore had <PERSON>'s Invisibility Cloak."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Her personality is brought to life through her handwriting and warm, loving words in the letter, providing <PERSON> with a powerful, tangible connection to her.", "community": 1, "url": "#character/lily_potter", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "albus_dumbledore", "attributes": {"label": "Albus Dumbledore", "name": "Albus Dumbledore", "stories": 5, "chapter_importance": 7, "presence_level": "mentioned", "emotional_state": "mysterious", "actions": ["(Posthumously) Is the subject of <PERSON>'s growing resentment and doubt due to <PERSON><PERSON>'s accusations.", "(Posthumously) Was mentioned in <PERSON>'s letter as having borrowed <PERSON>'s Invisibility Cloak.", "(Posthumously) Was the subject of 'incredible stories' told by <PERSON><PERSON><PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON><PERSON>'s character is posthumously developed as even more enigmatic. His foresight and cleverness are evident, but his methods raise questions about how much he trusted <PERSON>, adding a layer of complexity to <PERSON>'s memory of him.", "community": 3, "url": "#character/albus_dumbledore", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "mundungus_fletcher", "attributes": {"label": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "stories": 4, "chapter_importance": 8, "presence_level": "mentioned", "emotional_state": "thievish", "actions": ["Suspected of having pilfered from Grimmauld Place after the Order left.", "Identified by <PERSON><PERSON><PERSON> as the thief who stole many Black family heirlooms, including <PERSON><PERSON>'s locket."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON><PERSON> is shown to be exactly as described: a cowardly, unreliable petty thief. However, his actions are pivotal as he provides the crucial clue to the locket's current whereabouts, making his brief, coerced contribution immensely important to the plot.", "community": 0, "url": "#character/mundungus_fletcher", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "lord_vol<PERSON><PERSON>t", "attributes": {"label": "Lord <PERSON>", "name": "Lord <PERSON>", "stories": 7, "chapter_importance": 7, "presence_level": "mentioned", "emotional_state": "ruthless", "actions": ["(In the past) Required an elf (<PERSON><PERSON><PERSON>) to test the defenses of his Ho<PERSON><PERSON>x.", "(In the past) <PERSON><PERSON> to the sea cave and forced him to drink the emerald potion.", "(In the past) Placed the locket <PERSON><PERSON><PERSON><PERSON> in the basin and left <PERSON><PERSON><PERSON> to die on the island."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON><PERSON>'s power and ruthlessness are heavily reinforced. His ability to fly is a new, terrifying development, and his targeted killing of <PERSON><PERSON><PERSON> and torture of <PERSON><PERSON><PERSON><PERSON> demonstrate his strategic and merciless nature.", "community": 0, "url": "#character/lord_voldemort", "x": 0, "y": 0, "size": 1, "color": "#666666"}}], "edges": [{"source": "harry_potter", "target": "kreacher", "attributes": {"weight": 9, "relationship_type": "alliance", "interaction_summary": "<PERSON>'s relationship with <PERSON><PERSON><PERSON> undergoes a dramatic transformation from master-servant antagonism to a purposeful alliance. By showing unexpected kindness and providing <PERSON><PERSON><PERSON> with a mission that honors <PERSON><PERSON>, <PERSON> secures the elf's powerful loyalty.", "emotional_intensity": 9, "plot_significance": 10}}, {"source": "harry_potter", "target": "hermione_granger", "attributes": {"weight": 8, "relationship_type": "friendship", "interaction_summary": "<PERSON><PERSON><PERSON> provides support to <PERSON> but they experience some conflict over his growing obsession with <PERSON><PERSON><PERSON><PERSON>'s past. <PERSON><PERSON><PERSON> tries to ground him in their mission and defend <PERSON><PERSON><PERSON><PERSON>'s memory, while <PERSON> feels she is trying to stop him from learning the truth.", "emotional_intensity": 6, "plot_significance": 7}}, {"source": "harry_potter", "target": "albus_dumbledore", "attributes": {"weight": 8, "relationship_type": "conflict", "interaction_summary": "This relationship exists entirely in <PERSON>'s mind during this chapter. His grief is morphing into resentment and distrust, fueled by <PERSON><PERSON>'s gossip and the mysterious clues in his mother's letter. He questions <PERSON><PERSON><PERSON><PERSON>'s motives and whether he ever truly cared for <PERSON>.", "emotional_intensity": 8, "plot_significance": 8}}, {"source": "hermione_granger", "target": "kreacher", "attributes": {"weight": 7, "relationship_type": "protection", "interaction_summary": "<PERSON><PERSON><PERSON> shows profound empathy for <PERSON><PERSON><PERSON>, weeping at his story and attempting to comfort him physically. She understands and defends his actions, explaining them to <PERSON> and <PERSON>, and is horrified by his ingrained self-punishment, cementing her role as an advocate for his kind.", "emotional_intensity": 8, "plot_significance": 7}}, {"source": "kreacher", "target": "regulus_arcturus_black", "attributes": {"weight": 10, "relationship_type": "family", "interaction_summary": "Through <PERSON><PERSON><PERSON>'s tale, a deeply loyal and affectionate relationship is revealed. <PERSON><PERSON> treated <PERSON><PERSON><PERSON> with kindness and trust, which the elf repaid with absolute devotion. <PERSON><PERSON>'s final act was to entrust <PERSON><PERSON><PERSON> with his secret mission and ensure the elf's survival.", "emotional_intensity": 10, "plot_significance": 10}}, {"source": "regulus_arcturus_black", "target": "lord_vol<PERSON><PERSON>t", "attributes": {"weight": 9, "relationship_type": "betrayal", "interaction_summary": "<PERSON><PERSON> was initially a loyal follower of <PERSON><PERSON><PERSON><PERSON>, joining the Death Eaters at sixteen. However, after learning about the Horcruxes (likely from <PERSON><PERSON><PERSON><PERSON>'s use of <PERSON><PERSON><PERSON>), he turned against the Dark Lord and sacrificed his life in an act of defiance and betrayal to steal the locket.", "emotional_intensity": 9, "plot_significance": 10}}]}