{"attributes": {}, "options": {"allowSelfLoops": false, "multi": false, "type": "undirected"}, "nodes": [{"key": "harry_potter", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 12, "chapter_importance": 10, "presence_level": "protagonist", "emotional_state": "grieving", "actions": ["Survives a motorbike crash after being chased by Death Eaters and <PERSON><PERSON><PERSON><PERSON>.", "Is magically healed by <PERSON>.", "Argues with <PERSON><PERSON> about his use of 'Expelliarmus' on <PERSON>.", "<PERSON><PERSON>ts his trust in everyone present, refusing to believe there is a traitor among them.", "Mourns the death of his owl, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.", "Experiences a painful vision of <PERSON><PERSON><PERSON><PERSON> torturing <PERSON><PERSON><PERSON><PERSON>.", "Reveals the content of his vision to <PERSON> and <PERSON><PERSON><PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON> is confronted with the severe costs of war, losing friends and mentors. He solidifies his moral stance against killing, even when criticized by allies. His assertion of trust in the face of possible betrayal shows a maturation, though <PERSON><PERSON> sees it as a dangerous echo of his father's tragic flaw. The return of his scar-link to <PERSON><PERSON><PERSON><PERSON> re-establishes a critical vulnerability.", "community": 1, "url": "#character/harry_potter", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "rubeus_hagrid", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 1, "chapter_importance": 7, "presence_level": "major", "emotional_state": "shaken", "actions": ["Survives the crash with <PERSON>.", "Expresses immense relief upon finding <PERSON> alive.", "Mourns for Hedwig and Mad-Eye.", "Gets stuck in the Weasleys' back door twice.", "Drinks a bottle of brandy for 'medicinal purposes'."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s role reinforces his unwavering loyalty and protective nature towards <PERSON>. He is shown as emotionally vulnerable, openly grieving the losses of the night.", "community": 0, "url": "#character/rubeus_hagrid", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "remus_lupin", "attributes": {"label": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "stories": 3, "chapter_importance": 8, "presence_level": "major", "emotional_state": "tense", "actions": ["Arrives supporting an unconscious, bleeding <PERSON>.", "Performs a security check on <PERSON> to confirm his identity.", "Announces that the Order has been betrayed.", "<PERSON><PERSON>ues with <PERSON> about his use of Expelliarmus.", "Identifies <PERSON><PERSON><PERSON> as <PERSON>'s attacker, using <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Goes with <PERSON> to recover <PERSON><PERSON><PERSON>'s body."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON> is shown at his absolute lowest point, consumed by self-hatred and fear to the point of wanting to abandon his pregnant wife. This is a dramatic departure from his usual calm and wise persona, revealing a deep well of despair and insecurity that leads him to act rashly and violently.", "community": 0, "url": "#character/remus_lupin", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "ted_tonks", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 1, "chapter_importance": 5, "presence_level": "supporting", "emotional_state": "anxious", "actions": ["Discovers <PERSON> and <PERSON><PERSON><PERSON> after they crash.", "Heals <PERSON>'s broken ribs, regrows his tooth, and mends his arm.", "Explains the protective charms around his house.", "Provides the Portkey for <PERSON> and <PERSON><PERSON><PERSON> to travel to the Burrow."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Introduced as a kind, capable, and helpful member of the Order, providing crucial aid to <PERSON> and <PERSON><PERSON><PERSON>.", "community": 0, "url": "#character/ted_tonks", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "andromeda_tonks", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 1, "chapter_importance": 4, "presence_level": "supporting", "emotional_state": "fearful", "actions": ["Tends to <PERSON><PERSON><PERSON>'s injuries.", "Expresses fear for her daughter, <PERSON><PERSON><PERSON><PERSON>.", "Is initially mistaken for her sister <PERSON><PERSON><PERSON> by <PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "Introduced as <PERSON><PERSON><PERSON><PERSON>'s mother. Her brief appearance establishes her family connection to both the light (<PERSON><PERSON>) and dark (<PERSON><PERSON><PERSON>) sides of the wizarding world, and her deep parental fear.", "community": 0, "url": "#character/andromeda_tonks", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "george_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 7, "presence_level": "supporting", "emotional_state": "dazed", "actions": ["Arrives unconscious with a severe, bleeding head wound.", "Wakes up and makes a pun about his missing ear ('holey').", "Asks why <PERSON> and <PERSON> are not at his sickbed, learning they are not yet back."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON>'s character is defined in this chapter by his resilience and humor in the face of horrific injury, a core trait he shares with his twin, <PERSON>. His injury is a stark, physical symbol of the war's cost.", "community": 4, "url": "#character/george_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "fred_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 6, "presence_level": "supporting", "emotional_state": "shocked", "actions": ["Arrives safely with his father.", "Is terrified and speechless upon seeing <PERSON>'s injury.", "Responds to <PERSON>'s pun with mock criticism.", "Vocally supports <PERSON>'s declaration of trust in the group."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON>'s initial terror shows a rare crack in his jocular persona, revealing the depth of his bond with <PERSON>. His quick return to banter and his support for <PERSON> show his loyalty and coping mechanisms.", "community": 4, "url": "#character/fred_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "hermione_granger", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 9, "chapter_importance": 6, "presence_level": "supporting", "emotional_state": "relieved", "actions": ["Arrives safely with <PERSON>.", "Hugs <PERSON> and <PERSON> with immense relief.", "Listens to the reports of the other groups.", "Becomes terrified by <PERSON>'s vision and urges him to close his mind to <PERSON><PERSON><PERSON><PERSON>."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s character is defined by immense sacrifice and foresight in this chapter. Her decision to erase her parents' memories is a shocking display of maturity and dedication to the cause, establishing her as an equal partner in the mission's burdens, not just a follower.", "community": 4, "url": "#character/hermione_granger", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "ron_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 9, "chapter_importance": 6, "presence_level": "supporting", "emotional_state": "dazed", "actions": ["Arrives safely with <PERSON><PERSON>.", "Reports that he stunned a Death Eater.", "Is hugged tightly by <PERSON><PERSON><PERSON>.", "Joins <PERSON> in the garden to provide support and insist he stay."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON> demonstrates immense loyalty and maturity in his desire to protect <PERSON><PERSON><PERSON> from the new anti-Muggle-born laws. While still restless and prone to bickering, his core values of friendship and protection are strongly affirmed. He provides a more balanced perspective on the Lupin conflict than either <PERSON> or <PERSON><PERSON><PERSON>.", "community": 4, "url": "#character/ron_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "bill_weasley", "attributes": {"label": "<PERSON>", "name": "<PERSON>", "stories": 2, "chapter_importance": 7, "presence_level": "supporting", "emotional_state": "grim", "actions": ["Arrives safely with <PERSON><PERSON><PERSON>.", "Delivers the news of <PERSON><PERSON><PERSON>'s death.", "Recounts how <PERSON><PERSON><PERSON><PERSON> killed <PERSON><PERSON><PERSON> after <PERSON><PERSON><PERSON><PERSON> panicked.", "Argues that <PERSON><PERSON><PERSON><PERSON>'s panic, not betrayal, was the cause.", "Goes with <PERSON><PERSON> to recover <PERSON><PERSON><PERSON>'s body."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON> steps into a leadership role, delivering the hardest news with grim resolve and offering a rational counter-argument to the immediate suspicion of betrayal. His sense of duty is paramount.", "community": 4, "url": "#character/bill_weasley", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "alastor_moody", "attributes": {"label": "<PERSON><PERSON><PERSON> '<PERSON>-Eye' <PERSON>", "name": "<PERSON><PERSON><PERSON> '<PERSON>-Eye' <PERSON>", "stories": 5, "chapter_importance": 9, "presence_level": "mentioned", "emotional_state": "N/A", "actions": ["Was killed by a direct curse from <PERSON><PERSON><PERSON><PERSON>.", "Fell from his broom after his partner, <PERSON><PERSON><PERSON><PERSON>, Disapparated."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "His death establishes him as the 'Fallen Warrior' of the chapter title and serves as a major blow to the Order, removing one of its toughest and most experienced members and escalating the sense of dread and loss.", "community": 0, "url": "#character/alastor_moody", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "lord_vol<PERSON><PERSON>t", "attributes": {"label": "Lord <PERSON>", "name": "Lord <PERSON>", "stories": 7, "chapter_importance": 9, "presence_level": "mentioned", "emotional_state": "furious", "actions": ["Personally joined the chase to capture <PERSON>.", "Demonstrated the ability to fly without a broom.", "Killed <PERSON>-<PERSON>.", "Tortured <PERSON><PERSON><PERSON> for information in <PERSON>'s vision."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON><PERSON>'s power and ruthlessness are heavily reinforced. His ability to fly is a new, terrifying development, and his targeted killing of <PERSON><PERSON><PERSON> and torture of <PERSON><PERSON><PERSON><PERSON> demonstrate his strategic and merciless nature.", "community": 0, "url": "#character/lord_voldemort", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "mundungus_fletcher", "attributes": {"label": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "stories": 4, "chapter_importance": 6, "presence_level": "mentioned", "emotional_state": "panicked", "actions": ["Suggested the 'seven Potters' plan.", "Panicked when <PERSON><PERSON><PERSON><PERSON> attacked him and <PERSON><PERSON><PERSON>.", "Disapparated, abandoning Mad-Eye."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON><PERSON> is shown to be exactly as described: a cowardly, unreliable petty thief. However, his actions are pivotal as he provides the crucial clue to the locket's current whereabouts, making his brief, coerced contribution immensely important to the plot.", "community": 0, "url": "#character/mundungus_fletcher", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "severus_snape", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 4, "chapter_importance": 6, "presence_level": "mentioned", "emotional_state": "N/A", "actions": ["Participated in the chase with the Death Eaters.", "Used the curse '<PERSON><PERSON><PERSON><PERSON><PERSON>' to cut off <PERSON>'s ear."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s allegiance is further cemented with the Death Eaters in the eyes of the Order, as <PERSON><PERSON> identifies his signature dark spell as the cause of <PERSON>'s injury.", "community": 0, "url": "#character/severus_snape", "x": 0, "y": 0, "size": 1, "color": "#666666"}}, {"key": "hedwig", "attributes": {"label": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "stories": 3, "chapter_importance": 5, "presence_level": "mentioned", "emotional_state": "N/A", "actions": ["Was hit by a curse and killed during the initial chase."], "image": "placeholder.jpg", "image_url": "images/placeholder.jpg", "description": "<PERSON><PERSON><PERSON>'s death is the first major loss of the chapter, symbolizing the end of <PERSON>'s childhood and his link to a more innocent time in the magical world. Her death deeply affects <PERSON>.", "community": 0, "url": "#character/hedwig", "x": 0, "y": 0, "size": 1, "color": "#666666"}}], "edges": [{"source": "harry_potter", "target": "remus_lupin", "attributes": {"weight": 8, "relationship_type": "conflict", "interaction_summary": "<PERSON><PERSON> confirms <PERSON> is not an imposter, but then immediately confronts him, angrily criticizing his use of Expelliarm<PERSON> as a naive and suicidal tactic. <PERSON> defiantly defends his moral choice not to kill, leading to a tense and unresolved argument between the two allies.", "emotional_intensity": 8, "plot_significance": 9}}, {"source": "harry_potter", "target": "rubeus_hagrid", "attributes": {"weight": 7, "relationship_type": "protection", "interaction_summary": "After crash-landing, <PERSON>'s first concern is for an unconscious <PERSON><PERSON><PERSON>. When they are reunited, <PERSON><PERSON><PERSON> pulls <PERSON> into a massive, rib-cracking hug, expressing his relief. <PERSON><PERSON><PERSON> later tries to comfort <PERSON> over <PERSON><PERSON><PERSON>'s death, reinforcing their strong, protective bond.", "emotional_intensity": 7, "plot_significance": 6}}, {"source": "harry_potter", "target": "hermione_granger", "attributes": {"weight": 8, "relationship_type": "friendship", "interaction_summary": "<PERSON><PERSON><PERSON> expresses immense relief upon seeing <PERSON> is safe, throwing herself into his arms. Later, after <PERSON> reveals his vision of <PERSON><PERSON><PERSON><PERSON>, she becomes terrified for him and passionately urges him to close his mind, showing her deep care and concern for his mental well-being.", "emotional_intensity": 8, "plot_significance": 7}}, {"source": "remus_lupin", "target": "bill_weasley", "attributes": {"weight": 6, "relationship_type": "alliance", "interaction_summary": "After the trauma of the night, <PERSON> and <PERSON><PERSON> demonstrate their shared commitment and sense of duty. Without hesitation, <PERSON> agrees to join <PERSON><PERSON> on the dangerous mission to recover <PERSON><PERSON><PERSON>'s body, forgoing any personal rest.", "emotional_intensity": 5, "plot_significance": 6}}, {"source": "george_weasley", "target": "fred_weasley", "attributes": {"weight": 8, "relationship_type": "family", "interaction_summary": "<PERSON> is visibly terrified seeing the extent of <PERSON>'s injury, but <PERSON> diffuses the tension with a pun about his missing ear. <PERSON>'s response of calling the joke 'pathetic' shows a return to their normal dynamic, masking his profound relief.", "emotional_intensity": 9, "plot_significance": 6}}]}