{"name": "MARVEL graphs", "version": "1.0.0", "description": "Visualization of network maps of Marvel characters and creators featured together within same comics stories as registered within Marvel's API", "license": "AGPL v3.0", "main": "index.ts", "scripts": {"importpako": "cp node_modules/pako/dist/pako_inflate.min.js .", "build": "npm run importpako && kotatsu build client --config ./webpack.config.js --typescript index.ts --production -o build/bundle.js", "start": "npm run importpako && kotatsu serve --config ./webpack.config.js --typescript index.ts --public / ./", "builddata": "python bin/download_data.py && npm run rebuildgraphs", "rebuildgraphs": "npm run savepositions && npm run preparegraphs && npm run aligngraphs", "savepositions": "for entity in characters creators; do cp data/Marvel_${entity}_by_stories.json.gz data/Marvel_${entity}_by_stories.json.gz.positions; done", "preparegraphs": "ls data/*stories*.gexf | while read f; do node spatialize-network.js $f; echo; done", "preparetestgraphs": "ls data/*stories*.gexf | while read f; do node spatialize-network.js $f 100 100; echo; done", "aligngraphs": "for entity in characters creators; do for siz in '.' '_full.'; do node align-networks.js data/Marvel_${entity}_by_stories${siz}json.gz data/Marvel_${entity}_by_stories.json.gz.positions; done; done"}, "dependencies": {"@yomguithereal/sigma-experiments-renderers": "0.2.1", "graphology": "^0.25.0", "graphology-communities-louvain": "^2.0.1", "graphology-gexf": "^0.10.2", "graphology-layout": "^0.6.1", "graphology-layout-forceatlas2": "^0.10.0", "graphology-layout-noverlap": "^0.4.2", "pako": "^2.1.0", "papaparse": "^5.3.2", "sigma": "^3.0.0-alpha2"}, "devDependencies": {"kotatsu": "^0.22.3", "raw-loader": "^4.0.2", "typescript": "4.4.4"}}