#!/bin/bash

echo "🚀 Compiling All Harry Potter Data"
echo "=================================="

# Step 1: Process individual chapters for chapter-by-chapter view
echo "📖 Step 1: Processing individual chapters..."
python bin/process_hp_chapters.py
if [ $? -ne 0 ]; then
    echo "❌ Error processing individual chapters"
    exit 1
fi

# Step 2: Process all_chapters.json for global view
echo "🌍 Step 2: Processing global data from all_chapters.json..."
python bin/process_all_chapters.py
if [ $? -ne 0 ]; then
    echo "❌ Error processing global data"
    exit 1
fi

# Step 3: Spatialize chapter networks
echo "🎯 Step 3: Spatializing chapter networks..."
node spatialize-hp-chapters.js
if [ $? -ne 0 ]; then
    echo "❌ Error spatializing chapter networks"
    exit 1
fi

# Step 4: Spatialize global network
echo "🌐 Step 4: Spatializing global network..."
node spatialize-hp-global.js
if [ $? -ne 0 ]; then
    echo "❌ Error spatializing global network"
    exit 1
fi

# Step 5: Generate chapter metadata (from hp_data_processor.py)
echo "📚 Step 5: Generating chapter metadata..."
python bin/hp_data_processor.py > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Error generating chapter metadata"
    exit 1
fi

# Step 6: Test the complete system
echo "🔍 Step 6: Testing complete system..."
node test-complete-system.js
if [ $? -ne 0 ]; then
    echo "❌ Error in system test"
    exit 1
fi

echo ""
echo "✅ All data compilation completed successfully!"
echo ""
echo "📋 Summary:"
echo "- Global view: Uses consolidated data from all_chapters.json"
echo "- Chapter views: Use individual chapter data (c1.json through c12.json)"
echo "- All networks are properly spatialized"
echo "- Chapter metadata is available"
echo ""
echo "🎉 The Harry Potter knowledge graph is ready!"
echo "   - Global view shows consolidated characters and relationships across all chapters"
echo "   - Chapter-by-chapter view shows detailed interactions per chapter"
echo "   - Both views are fully functional and optimized"
