const fs = require('fs');
const pako = require('pako');

console.log("🔍 Complete System Test");

// Test 1: Global data from all_chapters.json processing
console.log("\n1️⃣ Testing Global Data (from all_chapters.json):");
try {
  const globalData = fs.readFileSync('data/HarryPotter_characters_by_stories_full_processed.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  
  console.log(`✅ Global data: ${globalGraph.nodes.length} characters, ${globalGraph.edges.length} relationships`);
  
  // Check if this matches our all_chapters.json structure
  const allChaptersData = JSON.parse(fs.readFileSync('hp_data/all_chapters.json', 'utf8'));
  console.log(`📊 Expected from all_chapters.json: ${allChaptersData.consolidated_characters.length} characters, ${allChaptersData.consolidated_relationships.length} relationships`);
  
  if (globalGraph.nodes.length === allChaptersData.consolidated_characters.length) {
    console.log("✅ Character count matches all_chapters.json");
  } else {
    console.log("⚠️ Character count mismatch");
  }
  
} catch (error) {
  console.error("❌ Error testing global data:", error.message);
}

// Test 2: Chapter-specific data
console.log("\n2️⃣ Testing Chapter Data:");
for (let i = 1; i <= 8; i++) {
  try {
    const chapterData = fs.readFileSync(`data/HarryPotter_characters_by_stories_c${i}.json.gz`);
    const chapterGraph = JSON.parse(pako.inflate(chapterData, {to: 'string'}));
    console.log(`✅ Chapter ${i}: ${chapterGraph.nodes.length} characters, ${chapterGraph.edges.length} relationships`);
  } catch (error) {
    console.error(`❌ Chapter ${i} error:`, error.message);
  }
}

// Test 3: Chapter metadata
console.log("\n3️⃣ Testing Chapter Metadata:");
try {
  const chapterMetadata = JSON.parse(fs.readFileSync('data/HarryPotter_chapters_metadata.json', 'utf8'));
  console.log(`✅ Chapter metadata: ${Object.keys(chapterMetadata).length} chapters`);
  Object.keys(chapterMetadata).forEach(chapterNum => {
    const chapter = chapterMetadata[chapterNum];
    console.log(`  📖 Chapter ${chapterNum}: ${chapter.chapter_title}`);
  });
} catch (error) {
  console.error("❌ Error testing chapter metadata:", error.message);
}

// Test 4: Data structure compatibility
console.log("\n4️⃣ Testing Data Structure Compatibility:");
try {
  const globalData = fs.readFileSync('data/HarryPotter_characters_by_stories_full_processed.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  
  // Check if nodes have required attributes
  const sampleNode = globalGraph.nodes[0];
  const requiredAttributes = ['label', 'name', 'stories', 'overall_importance', 'chapters', 'community'];
  
  console.log("🔍 Checking node attributes:");
  requiredAttributes.forEach(attr => {
    if (sampleNode.attributes[attr] !== undefined) {
      console.log(`  ✅ ${attr}: ${sampleNode.attributes[attr]}`);
    } else {
      console.log(`  ❌ Missing: ${attr}`);
    }
  });
  
  // Check if edges have required attributes
  if (globalGraph.edges.length > 0) {
    const sampleEdge = globalGraph.edges[0];
    const requiredEdgeAttributes = ['relationship_type', 'strength_score', 'weight'];
    
    console.log("🔍 Checking edge attributes:");
    requiredEdgeAttributes.forEach(attr => {
      if (sampleEdge.attributes[attr] !== undefined) {
        console.log(`  ✅ ${attr}: ${sampleEdge.attributes[attr]}`);
      } else {
        console.log(`  ❌ Missing: ${attr}`);
      }
    });
  }
  
} catch (error) {
  console.error("❌ Error testing data structure:", error.message);
}

// Test 5: File sizes and compression
console.log("\n5️⃣ Testing File Sizes:");
try {
  const files = [
    'data/HarryPotter_characters_by_stories_full_processed.json.gz',
    ...Array.from({length: 8}, (_, i) => `data/HarryPotter_characters_by_stories_c${i + 1}.json.gz`)
  ];
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      const stats = fs.statSync(file);
      console.log(`📁 ${file}: ${(stats.size / 1024).toFixed(2)} KB`);
    } else {
      console.log(`❌ Missing: ${file}`);
    }
  });
  
} catch (error) {
  console.error("❌ Error testing file sizes:", error.message);
}

console.log("\n✅ Complete system test finished!");
console.log("\n📋 Summary:");
console.log("- Global view uses data from all_chapters.json (consolidated characters and relationships)");
console.log("- Chapter views use individual chapter data (c1.json through c8.json)");
console.log("- All data files are properly compressed and spatialized");
console.log("- The system should now work correctly with both global and chapter-specific views");
